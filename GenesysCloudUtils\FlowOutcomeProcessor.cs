using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using DetInt = GenesysCloudDefDetailedInteractions;
using Interactions = GenesysCloudDefInteractionSegments;

namespace GenesysCloudUtils
{
    /// <summary>
    /// Centralized processor for flow outcome data that provides consistent error handling,
    /// logging, and thread-safe operations across all flow outcome processing scenarios.
    /// </summary>
    public class FlowOutcomeProcessor
    {
        private readonly ILogger? _logger;
        private readonly TimeZoneInfo _appTimeZone;

        // Thread-safe counters for tracking processing statistics
        private static int _globalProcessedCount = 0;
        private static int _globalDuplicateCount = 0;
        private static int _globalFailedCount = 0;
        private static readonly object _counterLock = new object();

        // Semaphore for controlling concurrent processing
        private static readonly SemaphoreSlim _processingLock = new SemaphoreSlim(Environment.ProcessorCount, Environment.ProcessorCount);

        // Properties to access counters safely
        public static int GlobalProcessedCount => Interlocked.CompareExchange(ref _globalProcessedCount, 0, 0);
        public static int GlobalDuplicateCount => Interlocked.CompareExchange(ref _globalDuplicateCount, 0, 0);
        public static int GlobalFailedCount => Interlocked.CompareExchange(ref _globalFailedCount, 0, 0);

        public FlowOutcomeProcessor(ILogger? logger, TimeZoneInfo appTimeZone)
        {
            _logger = logger;
            _appTimeZone = appTimeZone ?? throw new ArgumentNullException(nameof(appTimeZone));
        }

        /// <summary>
        /// Resets global counters - useful for testing or when starting a new processing session
        /// </summary>
        public static void ResetGlobalCounters()
        {
            Interlocked.Exchange(ref _globalProcessedCount, 0);
            Interlocked.Exchange(ref _globalDuplicateCount, 0);
            Interlocked.Exchange(ref _globalFailedCount, 0);
        }

        /// <summary>
        /// Processes flow outcomes from Interactions.Flow objects (used in GetDetailInteractionDataFromGC)
        /// </summary>
        public async Task<FlowOutcomeProcessingResult> ProcessFlowOutcomesAsync(
            IEnumerable<Interactions.Flow> flows,
            string conversationId,
            DateTime conversationStart,
            DateTime conversationEnd,
            DataTable flowOutcomesTable,
            bool throwOnInvalidDates = true)
        {
            var result = new FlowOutcomeProcessingResult();
            
            if (flows == null)
            {
                _logger?.LogDebug("No flows provided for conversation {ConversationId}", conversationId);
                return result;
            }

            foreach (var flow in flows)
            {
                if (flow?.outcomes == null) continue;

                var outcomes = flow.outcomes.ToList();
                foreach (var outcome in outcomes)
                {
                    try
                    {
                        if (outcome?.flowOutcomeId == null) continue;

                        var flowOutcomeRow = await ProcessSingleFlowOutcomeAsync(
                            flowOutcomesTable,
                            conversationId,
                            conversationStart,
                            conversationEnd,
                            flow.flowId,
                            flow.flowName,
                            flow.flowVersion,
                            flow.flowType,
                            outcome,
                            throwOnInvalidDates);

                        if (flowOutcomeRow != null)
                        {
                            result.ProcessedCount++;
                            // _logger?.LogDebug("Processed flow outcome {FlowOutcomeId} for conversation {ConversationId}",
                            //     outcome.flowOutcomeId, conversationId);
                        }
                    }
                    catch (System.Data.ConstraintException ex)
                    {
                        _logger?.LogDebug(ex, "Duplicate flow outcome detected for conversation {ConversationId}", conversationId);
                        result.DuplicateCount++;
                    }
                    catch (FormatException ex) when (throwOnInvalidDates)
                    {
                        _logger?.LogError(ex, "Invalid date format in flow outcome for conversation {ConversationId}", conversationId);
                        result.FailedCount++;
                        throw; // Re-throw to maintain existing behavior when throwOnInvalidDates is true
                    }
                    catch (FormatException ex) when (!throwOnInvalidDates)
                    {
                        _logger?.LogWarning(ex, "Invalid date format in flow outcome for conversation {ConversationId}. Skipping outcome.", conversationId);
                        result.FailedCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Error processing flow outcome for conversation {ConversationId}", conversationId);
                        result.FailedCount++;
                        
                        if (throwOnInvalidDates)
                            throw;
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// Processes flow outcomes from DetInt.Flow objects (used in ProcessDataAsync)
        /// </summary>
        public async Task<FlowOutcomeProcessingResult> ProcessFlowOutcomesAsync(
            IEnumerable<DetInt.Flow> flows,
            string conversationId,
            DateTime conversationStart,
            DateTime conversationEnd,
            DataTable flowOutcomesTable,
            bool throwOnInvalidDates = true)
        {
            var result = new FlowOutcomeProcessingResult();
            
            if (flows == null)
            {
                _logger?.LogDebug("No flows provided for conversation {ConversationId}", conversationId);
                return result;
            }

            foreach (var flow in flows)
            {
                if (flow?.outcomes == null) continue;

                var outcomes = flow.outcomes.ToList();
                foreach (var outcome in outcomes)
                {
                    try
                    {
                        if (outcome?.flowOutcomeId == null) continue;

                        var flowOutcomeRow = await ProcessSingleDetIntFlowOutcomeAsync(
                            flowOutcomesTable,
                            conversationId,
                            conversationStart,
                            conversationEnd,
                            flow.flowId,
                            flow.flowName,
                            flow.flowVersion,
                            flow.flowType,
                            outcome,
                            throwOnInvalidDates);

                        if (flowOutcomeRow != null)
                        {
                            result.ProcessedCount++;
                            // _logger?.LogDebug("Processed flow outcome {FlowOutcomeId} for conversation {ConversationId}",
                            //     outcome.flowOutcomeId, conversationId);
                        }
                    }
                    catch (System.Data.ConstraintException ex)
                    {
                        _logger?.LogDebug(ex, "Duplicate flow outcome detected for conversation {ConversationId}", conversationId);
                        result.DuplicateCount++;
                    }
                    catch (FormatException ex) when (throwOnInvalidDates)
                    {
                        _logger?.LogError(ex, "Invalid date format in flow outcome for conversation {ConversationId}", conversationId);
                        result.FailedCount++;
                        throw; // Re-throw to maintain existing behavior when throwOnInvalidDates is true
                    }
                    catch (FormatException ex) when (!throwOnInvalidDates)
                    {
                        _logger?.LogWarning(ex, "Invalid date format in flow outcome for conversation {ConversationId}. Skipping outcome.", conversationId);
                        result.FailedCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Error processing flow outcome for conversation {ConversationId}", conversationId);
                        result.FailedCount++;
                        
                        if (throwOnInvalidDates)
                            throw;
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// Thread-safe method to add flow outcome rows to the DataTable with duplicate checking
        /// </summary>
        public void AddFlowOutcomeRowSafely(DataTable flowOutcomesTable, DataRow flowOutcomeRow, object lockObject)
        {
            if (flowOutcomeRow == null) return;

            lock (lockObject)
            {
                try
                {
                    // Check for duplicates before adding
                    string keyId = flowOutcomeRow["keyid"]?.ToString();
                    if (!string.IsNullOrEmpty(keyId))
                    {
                        var existingRow = flowOutcomesTable.AsEnumerable()
                            .FirstOrDefault(row => row.Field<string>("keyid") == keyId);
                        
                        if (existingRow == null)
                        {
                            flowOutcomesTable.Rows.Add(flowOutcomeRow);
                        }
                        else
                        {
                            _logger?.LogDebug("Duplicate flow outcome with keyid {KeyId} already exists. Skipping.", keyId);
                        }
                    }
                    else
                    {
                        flowOutcomesTable.Rows.Add(flowOutcomeRow);
                    }
                }
                catch (System.Data.ConstraintException ex)
                {
                    // Suppress individual constraint violation debug messages to reduce log noise
                    // These are expected when the same flow outcome is processed multiple times
                }
                catch (Exception ex)
                {
                    string conversationId = flowOutcomeRow["conversationid"]?.ToString() ?? "Unknown";
                    _logger?.LogError(ex, "Error adding flow outcome row to table for conversation {ConversationId}", conversationId);
                    throw;
                }
            }
        }

        /// <summary>
        /// Logs a summary of flow outcome processing results with performance metrics
        /// </summary>
        public void LogProcessingSummary(FlowOutcomeProcessingResult result, string context = "", TimeSpan? processingTime = null)
        {
            if (result.TotalProcessed > 0)
            {
                if (processingTime.HasValue)
                {
                    _logger?.LogInformation("Flow outcome processing {Context}: {ProcessedCount} processed, {DuplicateCount} duplicates, {FailedCount} failed, {TotalCount} total in {ElapsedSeconds:F2}s",
                        context, result.ProcessedCount, result.DuplicateCount, result.FailedCount, result.TotalProcessed, processingTime.Value.TotalSeconds);
                }
                else
                {
                    _logger?.LogInformation("Flow outcome processing {Context}: {ProcessedCount} processed, {DuplicateCount} duplicates, {FailedCount} failed, {TotalCount} total",
                        context, result.ProcessedCount, result.DuplicateCount, result.FailedCount, result.TotalProcessed);
                }
            }
            else
            {
                _logger?.LogDebug("Flow outcome processing {Context}: No outcomes to process", context);
            }
        }

        /// <summary>
        /// Processes flow outcomes with enhanced error handling and performance tracking
        /// </summary>
        public async Task<FlowOutcomeProcessingResult> ProcessFlowOutcomesBatchAsync(
            IEnumerable<Interactions.Flow> flows,
            string conversationId,
            DateTime conversationStart,
            DateTime conversationEnd,
            DataTable flowOutcomesTable,
            bool throwOnInvalidDates = true,
            string batchContext = "")
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new FlowOutcomeProcessingResult();

            try
            {
                _logger?.LogDebug("Starting flow outcome processing for conversation {ConversationId} in batch {BatchContext}",
                    conversationId, batchContext);

                result = await ProcessFlowOutcomesAsync(flows, conversationId, conversationStart, conversationEnd, flowOutcomesTable, throwOnInvalidDates);

                stopwatch.Stop();

                if (result.TotalProcessed > 0)
                {
                    _logger?.LogDebug("Completed flow outcome processing for conversation {ConversationId}: {ProcessedCount} processed, {DuplicateCount} duplicates, {FailedCount} failed in {ElapsedMs}ms",
                        conversationId, result.ProcessedCount, result.DuplicateCount, result.FailedCount, stopwatch.ElapsedMilliseconds);
                }

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger?.LogError(ex, "Critical error processing flow outcomes for conversation {ConversationId} in batch {BatchContext} after {ElapsedMs}ms: {ErrorMessage}",
                    conversationId, batchContext, stopwatch.ElapsedMilliseconds, ex.Message);

                result.FailedCount++;

                // For critical errors, decide whether to continue or fail-fast based on error type
                if (ex is OutOfMemoryException || ex is StackOverflowException)
                {
                    _logger?.LogCritical(ex, "Critical system error during flow outcome processing - failing fast");
                    throw; // Fail-fast for critical system errors
                }

                // For other errors, continue processing but track the failure
                return result;
            }
        }

        /// <summary>
        /// Processes multiple conversations concurrently with proper thread safety and resource management
        /// </summary>
        public async Task<FlowOutcomeProcessingResult> ProcessConversationsBatchAsync<T>(
            IEnumerable<ConversationFlowData<T>> conversationFlows,
            DataTable flowOutcomesTable,
            bool throwOnInvalidDates = true,
            int maxConcurrency = 0,
            string batchContext = "")
        {
            if (maxConcurrency <= 0)
                maxConcurrency = Environment.ProcessorCount;

            var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
            var aggregateResult = new FlowOutcomeProcessingResult();
            var lockObject = new object();
            var stopwatch = Stopwatch.StartNew();

            _logger?.LogInformation("Starting concurrent flow outcome processing for {ConversationCount} conversations with max concurrency {MaxConcurrency}",
                conversationFlows.Count(), maxConcurrency);

            var tasks = conversationFlows.Select(async conversationFlow =>
            {
                await semaphore.WaitAsync();
                try
                {
                    FlowOutcomeProcessingResult result;

                    if (typeof(T) == typeof(Interactions.Flow))
                    {
                        result = await ProcessFlowOutcomesAsync(
                            conversationFlow.Flows.Cast<Interactions.Flow>(),
                            conversationFlow.ConversationId,
                            conversationFlow.ConversationStart,
                            conversationFlow.ConversationEnd,
                            flowOutcomesTable,
                            throwOnInvalidDates);
                    }
                    else if (typeof(T) == typeof(DetInt.Flow))
                    {
                        result = await ProcessFlowOutcomesAsync(
                            conversationFlow.Flows.Cast<DetInt.Flow>(),
                            conversationFlow.ConversationId,
                            conversationFlow.ConversationStart,
                            conversationFlow.ConversationEnd,
                            flowOutcomesTable,
                            throwOnInvalidDates);
                    }
                    else
                    {
                        throw new ArgumentException($"Unsupported flow type: {typeof(T)}");
                    }

                    // Thread-safe aggregation of results
                    lock (lockObject)
                    {
                        aggregateResult.Merge(result);
                    }

                    // Update global counters atomically
                    Interlocked.Add(ref _globalProcessedCount, result.ProcessedCount);
                    Interlocked.Add(ref _globalDuplicateCount, result.DuplicateCount);
                    Interlocked.Add(ref _globalFailedCount, result.FailedCount);

                    return result;
                }
                finally
                {
                    semaphore.Release();
                }
            });

            await Task.WhenAll(tasks);
            stopwatch.Stop();

            _logger?.LogInformation("Completed concurrent flow outcome processing: {ProcessedCount} processed, {DuplicateCount} duplicates, {FailedCount} failed in {ElapsedSeconds:F2}s",
                aggregateResult.ProcessedCount, aggregateResult.DuplicateCount, aggregateResult.FailedCount, stopwatch.Elapsed.TotalSeconds);

            return aggregateResult;
        }

        /// <summary>
        /// Processes a single flow outcome from Interactions.Outcomes
        /// </summary>
        private async Task<DataRow?> ProcessSingleFlowOutcomeAsync(
            DataTable flowOutcomesTable,
            string conversationId,
            DateTime conversationStart,
            DateTime conversationEnd,
            string? flowId,
            string? flowName,
            string? flowVersion,
            string? flowType,
            Interactions.Outcomes outcome,
            bool throwOnInvalidDates)
        {
            var flowOutcomeRow = flowOutcomesTable.NewRow();

            // Set basic flow outcome data
            flowOutcomeRow["keyid"] = $"{conversationId}|{flowId}|{outcome.flowOutcomeId}";
            flowOutcomeRow["flowid"] = flowId;
            flowOutcomeRow["flowname"] = flowName;

            // Handle flow version parsing
            try
            {
                if (!string.IsNullOrEmpty(flowVersion))
                {
                    flowOutcomeRow["flowversion"] = decimal.Round(decimal.Parse(flowVersion), 2);
                }
                else
                {
                    flowOutcomeRow["flowversion"] = 1.0m;
                }
            }
            catch
            {
                flowOutcomeRow["flowversion"] = 1.0m;
            }

            flowOutcomeRow["flowtype"] = flowType;
            flowOutcomeRow["conversationid"] = conversationId;
            // conversationStart is already UTC from API deserialization
            flowOutcomeRow["conversationstartdate"] = conversationStart;
            flowOutcomeRow["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(conversationStart, _appTimeZone);

            // Handle conversation end date
            if (conversationEnd > DateTime.UtcNow.AddYears(-20))
            {
                // conversationEnd is already UTC from API deserialization
                flowOutcomeRow["conversationenddate"] = conversationEnd;
                flowOutcomeRow["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(conversationEnd, _appTimeZone);
            }
            else
            {
                flowOutcomeRow["conversationenddate"] = DBNull.Value;
                flowOutcomeRow["conversationenddateltc"] = DBNull.Value;
            }

            // Handle flow outcome start timestamp
            if (DateTime.TryParse(outcome.flowOutcomeStartTimestamp, CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal, out var parsedStartTimestamp))
            {
                // parsedStartTimestamp is already UTC due to DateTimeStyles.AdjustToUniversal, no need for .ToUniversalTime()
                var flowOutcomeStartDateUtc = parsedStartTimestamp;
                flowOutcomeRow["flowoutcomestartdate"] = flowOutcomeStartDateUtc;
                flowOutcomeRow["flowoutcomestartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(flowOutcomeStartDateUtc, _appTimeZone);
            }
            else
            {
                throw new FormatException($"Invalid date format in flowOutcomeStartTimestamp: '{outcome.flowOutcomeStartTimestamp}'");
            }

            // Handle flow outcome end timestamp
            if (!string.IsNullOrEmpty(outcome.flowOutcomeEndTimestamp))
            {
                if (DateTime.TryParse(outcome.flowOutcomeEndTimestamp, CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal, out var parsedEndTimestamp))
                {
                    // parsedEndTimestamp is already UTC due to DateTimeStyles.AdjustToUniversal, no need for .ToUniversalTime()
                    var flowOutcomeEndDateUtc = parsedEndTimestamp;
                    flowOutcomeRow["flowoutcomeenddate"] = flowOutcomeEndDateUtc;
                    flowOutcomeRow["flowoutcomeenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(flowOutcomeEndDateUtc, _appTimeZone);
                }
                else if (throwOnInvalidDates)
                {
                    throw new FormatException($"Invalid date format in flowOutcomeEndTimestamp: '{outcome.flowOutcomeEndTimestamp}'");
                }
                else
                {
                    _logger?.LogWarning("Invalid date format in flowOutcomeEndTimestamp: '{Value}' for conversation {ConversationId}. Using null instead.",
                        outcome.flowOutcomeEndTimestamp, conversationId);
                    flowOutcomeRow["flowoutcomeenddate"] = DBNull.Value;
                    flowOutcomeRow["flowoutcomeenddateltc"] = DBNull.Value;
                }
            }
            else
            {
                flowOutcomeRow["flowoutcomeenddate"] = DBNull.Value;
                flowOutcomeRow["flowoutcomeenddateltc"] = DBNull.Value;
            }

            // Set outcome data
            flowOutcomeRow["flowoutcome"] = outcome.flowOutcome;
            flowOutcomeRow["flowoutcomeid"] = outcome.flowOutcomeId;
            flowOutcomeRow["flowoutcomevalue"] = outcome.flowOutcomeValue;

            return flowOutcomeRow;
        }

        /// <summary>
        /// Processes a single flow outcome from DetInt.Outcomes
        /// </summary>
        private async Task<DataRow?> ProcessSingleDetIntFlowOutcomeAsync(
            DataTable flowOutcomesTable,
            string conversationId,
            DateTime conversationStart,
            DateTime conversationEnd,
            string? flowId,
            string? flowName,
            string? flowVersion,
            string? flowType,
            DetInt.Outcomes outcome,
            bool throwOnInvalidDates)
        {
            var flowOutcomeRow = flowOutcomesTable.NewRow();

            // Set basic flow outcome data
            flowOutcomeRow["keyid"] = $"{conversationId}|{flowId}|{outcome.flowOutcomeId}";
            flowOutcomeRow["flowid"] = flowId;
            flowOutcomeRow["flowname"] = flowName;

            // Handle flow version parsing
            try
            {
                if (!string.IsNullOrEmpty(flowVersion))
                {
                    flowOutcomeRow["flowversion"] = decimal.Round(decimal.Parse(flowVersion), 2);
                }
                else
                {
                    flowOutcomeRow["flowversion"] = 1.0m;
                }
            }
            catch
            {
                flowOutcomeRow["flowversion"] = 1.0m;
            }

            flowOutcomeRow["flowtype"] = flowType;
            flowOutcomeRow["conversationid"] = conversationId;
            // conversationStart is already UTC from API deserialization
            flowOutcomeRow["conversationstartdate"] = conversationStart;
            flowOutcomeRow["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(conversationStart, _appTimeZone);

            // Handle conversation end date
            if (conversationEnd > DateTime.UtcNow.AddYears(-20))
            {
                // conversationEnd is already UTC from API deserialization
                flowOutcomeRow["conversationenddate"] = conversationEnd;
                flowOutcomeRow["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(conversationEnd, _appTimeZone);
            }
            else
            {
                flowOutcomeRow["conversationenddate"] = DBNull.Value;
                flowOutcomeRow["conversationenddateltc"] = DBNull.Value;
            }

            // Handle flow outcome start timestamp
            if (DateTime.TryParse(outcome.flowOutcomeStartTimestamp, CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal, out var parsedStartTimestamp))
            {
                // parsedStartTimestamp is already UTC due to DateTimeStyles.AdjustToUniversal, no need for .ToUniversalTime()
                var flowOutcomeStartDateUtc = parsedStartTimestamp;
                flowOutcomeRow["flowoutcomestartdate"] = flowOutcomeStartDateUtc;
                flowOutcomeRow["flowoutcomestartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(flowOutcomeStartDateUtc, _appTimeZone);
            }
            else
            {
                throw new FormatException($"Invalid date format in flowOutcomeStartTimestamp: '{outcome.flowOutcomeStartTimestamp}'");
            }

            // Handle flow outcome end timestamp
            if (!string.IsNullOrEmpty(outcome.flowOutcomeEndTimestamp))
            {
                if (DateTime.TryParse(outcome.flowOutcomeEndTimestamp, CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal, out var parsedEndTimestamp))
                {
                    // parsedEndTimestamp is already UTC due to DateTimeStyles.AdjustToUniversal, no need for .ToUniversalTime()
                    var flowOutcomeEndDateUtc = parsedEndTimestamp;
                    flowOutcomeRow["flowoutcomeenddate"] = flowOutcomeEndDateUtc;
                    flowOutcomeRow["flowoutcomeenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(flowOutcomeEndDateUtc, _appTimeZone);
                }
                else if (throwOnInvalidDates)
                {
                    throw new FormatException($"Invalid date format in flowOutcomeEndTimestamp: '{outcome.flowOutcomeEndTimestamp}'");
                }
                else
                {
                    _logger?.LogWarning("Invalid date format in flowOutcomeEndTimestamp: '{Value}' for conversation {ConversationId}. Using null instead.",
                        outcome.flowOutcomeEndTimestamp, conversationId);
                    flowOutcomeRow["flowoutcomeenddate"] = DBNull.Value;
                    flowOutcomeRow["flowoutcomeenddateltc"] = DBNull.Value;
                }
            }
            else
            {
                flowOutcomeRow["flowoutcomeenddate"] = DBNull.Value;
                flowOutcomeRow["flowoutcomeenddateltc"] = DBNull.Value;
            }

            // Set outcome data
            flowOutcomeRow["flowoutcome"] = outcome.flowOutcome;
            flowOutcomeRow["flowoutcomeid"] = outcome.flowOutcomeId;
            flowOutcomeRow["flowoutcomevalue"] = outcome.flowOutcomeValue;

            return flowOutcomeRow;
        }

        /// <summary>
        /// Determines if an exception should trigger fail-fast behavior or allow continued processing
        /// </summary>
        private bool ShouldFailFast(Exception ex)
        {
            // Critical system errors that should halt processing immediately
            return ex is OutOfMemoryException ||
                   ex is StackOverflowException ||
                   ex is AccessViolationException ||
                   ex is AppDomainUnloadedException ||
                   ex is BadImageFormatException ||
                   ex is CannotUnloadAppDomainException ||
                   ex is ExecutionEngineException ||
                   ex is InvalidProgramException ||
                   ex is TypeLoadException;
        }

        /// <summary>
        /// Determines if an exception is retryable (transient error)
        /// </summary>
        private bool IsRetryableException(Exception ex)
        {
            // Network-related and transient errors that might succeed on retry
            return ex is System.Net.Http.HttpRequestException ||
                   ex is System.Net.Sockets.SocketException ||
                   ex is TimeoutException ||
                   (ex is InvalidOperationException && ex.Message.Contains("rate limit")) ||
                   (ex is Exception && ex.Message.Contains("timeout"));
        }

        /// <summary>
        /// Logs exception with appropriate level based on error type and context
        /// </summary>
        private void LogExceptionWithContext(Exception ex, string conversationId, string operation, string context = "")
        {
            var contextInfo = string.IsNullOrEmpty(context) ? "" : $" in {context}";

            if (ShouldFailFast(ex))
            {
                _logger?.LogCritical(ex, "Critical system error during {Operation} for conversation {ConversationId}{Context}: {ErrorMessage}",
                    operation, conversationId, contextInfo, ex.Message);
            }
            else if (IsRetryableException(ex))
            {
                _logger?.LogWarning(ex, "Transient error during {Operation} for conversation {ConversationId}{Context}: {ErrorMessage}",
                    operation, conversationId, contextInfo, ex.Message);
            }
            else if (ex is FormatException || ex is ArgumentException)
            {
                _logger?.LogWarning(ex, "Data format error during {Operation} for conversation {ConversationId}{Context}: {ErrorMessage}",
                    operation, conversationId, contextInfo, ex.Message);
            }
            else if (ex is System.Data.ConstraintException)
            {
                // Suppress individual constraint violation debug messages to reduce log noise
                // These are expected when the same flow outcome data is processed multiple times
            }
            else
            {
                _logger?.LogError(ex, "Unexpected error during {Operation} for conversation {ConversationId}{Context}: {ErrorMessage}",
                    operation, conversationId, contextInfo, ex.Message);
            }
        }
    }

    /// <summary>
    /// Result class for flow outcome processing operations
    /// </summary>
    public class FlowOutcomeProcessingResult
    {
        public int ProcessedCount { get; set; }
        public int DuplicateCount { get; set; }
        public int FailedCount { get; set; }
        public int RetryableFailedCount { get; set; }
        public int CriticalFailedCount { get; set; }

        public int TotalProcessed => ProcessedCount + DuplicateCount + FailedCount;
        public bool HasCriticalFailures => CriticalFailedCount > 0;
        public bool HasRetryableFailures => RetryableFailedCount > 0;

        /// <summary>
        /// Merges another result into this one
        /// </summary>
        public void Merge(FlowOutcomeProcessingResult other)
        {
            ProcessedCount += other.ProcessedCount;
            DuplicateCount += other.DuplicateCount;
            FailedCount += other.FailedCount;
            RetryableFailedCount += other.RetryableFailedCount;
            CriticalFailedCount += other.CriticalFailedCount;
        }
    }

    /// <summary>
    /// Data structure for holding conversation and flow information for batch processing
    /// </summary>
    public class ConversationFlowData<T>
    {
        public string ConversationId { get; set; } = string.Empty;
        public DateTime ConversationStart { get; set; }
        public DateTime ConversationEnd { get; set; }
        public IEnumerable<T> Flows { get; set; } = Enumerable.Empty<T>();

        public ConversationFlowData(string conversationId, DateTime conversationStart, DateTime conversationEnd, IEnumerable<T> flows)
        {
            ConversationId = conversationId;
            ConversationStart = conversationStart;
            ConversationEnd = conversationEnd;
            Flows = flows ?? Enumerable.Empty<T>();
        }
    }
}
