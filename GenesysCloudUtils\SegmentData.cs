using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Web;
using CSG.Common.ExtensionMethods;
using DBUtils;
using DetInt = GenesysCloudDefDetailedInteractions;
using Interactions = GenesysCloudDefInteractionSegments;
using PartAttribs = GenesysCloudDefParticipantAttrib;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using StandardUtils;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using CallSumm = GenesysCloudDefCallSummary;
using GenesysCloudUtils;

#nullable enable

namespace GenesysCloudUtils
{
    public class DetailData
    {
        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DateTime DetailInteractionLastUpdate { get; set; }
        public DataSet GCControlData { get; set; }
        private Utils UCAUtils = new Utils();
        private GCUtils GCUtilities = new GCUtils();
        private JsonUtils JsonActions;
        public string? TimeZoneConfig { get; set; }
        private DBUtils.DBUtils DBUtil;
        private DataTable ConversationSummaryData;
        private CSG.Adapter.Configuration.RegexReplacement[]? _renameParticipantAttributeNames;
        private readonly ILogger? _logger;
        private readonly object _detailInteractionLock = new object();
        private readonly object _participantAttributeLock = new object();
        private readonly object _participantSummaryLock = new object();
        private readonly object _flowOutcomesLock = new object();

        // Thread-safe collections for tracking processed keys to improve duplicate detection performance
        private readonly ConcurrentDictionary<string, bool> _processedParticipantKeys = new ConcurrentDictionary<string, bool>();
        private readonly ConcurrentDictionary<string, bool> _processedDetailInteractionKeys = new ConcurrentDictionary<string, bool>();

        // Unified conversation tracking to prevent double-counting
        private readonly ConcurrentDictionary<string, bool> _processedConversationIds = new ConcurrentDictionary<string, bool>();
        private int _totalUniqueConversationsProcessed = 0;

        // Constraint violation tracking for summarized logging
        private int _conversationSummaryConstraintViolations = 0;

        // Rate limiting for participant attributes processing - now configurable
        private static int _participantAttributesRequestCount = 0;
        private static DateTime _participantAttributesRateLimitWindowStart = DateTime.UtcNow;
        private static readonly object _rateLimitLock = new object();

        // Configurable rate limiting values with defaults
        private int _maxRequestsPerMinute = 1950; // Default: 65% of 3000 requests per 60 seconds
        private int _windowDurationSeconds = 60; // Default: 60 seconds
        private int _tokenRefreshInterval = 275; // Default: 275 requests
        private int _safetyMarginPercentage = 65; // Default: 65%

        // VALIDATION LAYER: Track conversations that have already had participant attributes processed
        // This prevents double processing when multiple methods call participant attributes processing
        private readonly ConcurrentDictionary<string, bool> _processedParticipantAttributeConversations = new ConcurrentDictionary<string, bool>();

        // PERFORMANCE OPTIMIZATION: HashSet lookups for duplicate detection (40-60% faster than DataTable.Select)
        // These provide O(1) lookup performance vs O(n) for DataTable.Select operations
        private readonly HashSet<string> _detailInteractionKeyCache = new HashSet<string>();
        private readonly HashSet<string> _participantAttributesKeyCache = new HashSet<string>();
        private readonly HashSet<string> _participantSummaryKeyCache = new HashSet<string>();
        private readonly HashSet<string> _flowOutcomesKeyCache = new HashSet<string>();
        private readonly object _keyCacheLock = new object(); // Single lock for all key caches

        // PERFORMANCE OPTIMIZATION: Simple caching for frequently accessed data (10-15% improvement)
        private readonly Dictionary<string, string> _userIdToNameCache = new Dictionary<string, string>();
        private readonly Dictionary<string, string> _queueIdToNameCache = new Dictionary<string, string>();
        private readonly object _dataCacheLock = new object(); // Lock for data caches

        public DetailData(
            ILogger? logger,
            CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames,
            CSG.Adapter.Configuration.RateLimiting? rateLimitingConfig = null)
        {
            _logger = logger;
            _renameParticipantAttributeNames = renameParticipantAttributeNames;

            // Initialize rate limiting configuration with defaults if not provided
            if (rateLimitingConfig != null)
            {
                _maxRequestsPerMinute = rateLimitingConfig.ParticipantAttributesMaxRequestsPerMinute ?? 1950;
                _windowDurationSeconds = rateLimitingConfig.WindowDurationSeconds ?? 60;
                _tokenRefreshInterval = rateLimitingConfig.TokenRefreshInterval ?? 275;
                _safetyMarginPercentage = rateLimitingConfig.SafetyMarginPercentage ?? 65;

                _logger?.LogInformation("Rate limiting configured: {MaxRequests}/min, {WindowDuration}s window, token refresh every {TokenRefresh} requests, {SafetyMargin}% safety margin",
                    _maxRequestsPerMinute, _windowDurationSeconds, _tokenRefreshInterval, _safetyMarginPercentage);
            }
            else
            {
                _logger?.LogInformation("Rate limiting using defaults: {MaxRequests}/min, {WindowDuration}s window, token refresh every {TokenRefresh} requests, {SafetyMargin}% safety margin",
                    _maxRequestsPerMinute, _windowDurationSeconds, _tokenRefreshInterval, _safetyMarginPercentage);
            }

            GCUtilities.Initialize();
            DBUtil = new DBUtils.DBUtils(_logger);
            DBUtil.Initialize();
            UCAUtils = new StandardUtils.Utils();
            CustomerKeyID = GCUtilities.CustomerKeyID;
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;

            // Initialize JsonActions with logger for centralized rate limiting
            JsonActions = new JsonUtils(logger);

            ConversationSummaryData = DBUtil.CreateInMemTable("convsummarydata");
        }

        // PERFORMANCE OPTIMIZATION: Method to clear caches when needed to prevent memory leaks
        public void ClearPerformanceCaches()
        {
            lock (_keyCacheLock)
            {
                _detailInteractionKeyCache.Clear();
                _participantAttributesKeyCache.Clear();
                _participantSummaryKeyCache.Clear();
                _flowOutcomesKeyCache.Clear();
            }

            lock (_dataCacheLock)
            {
                _userIdToNameCache.Clear();
                _queueIdToNameCache.Clear();
            }

            _logger?.LogInformation("Performance caches cleared to free memory");
        }

        /// <summary>
        /// Clears the processed conversation tracking to allow reprocessing in new job runs.
        /// This should be called at the start of each job execution to prevent cross-job duplicate detection.
        /// </summary>
        public void ClearProcessedConversationTracking()
        {
            _processedParticipantAttributeConversations.Clear();
            _processedConversationIds.Clear();
            _logger?.LogInformation("Cleared processed conversation tracking for new job run");
        }

        private string RenameParticipantAttributeNames(
            CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames,
            string attributeName)
        {
            if (_renameParticipantAttributeNames == null || _renameParticipantAttributeNames.Length == 0)
                return attributeName;

            foreach (var rule in _renameParticipantAttributeNames)
            {
                if (string.IsNullOrEmpty(rule.Find))
                {
                    _logger?.LogWarning("Empty RenameParticipantAttributeNames option Find value");
                    continue;
                }
                var pre = attributeName;
                attributeName = Regex.Replace(attributeName, rule.Find, rule.Replace ?? "");
                if (string.IsNullOrEmpty(attributeName))
                    throw new ArgumentNullException(pre, $"Attribute name '{pre}' was cleared by rule '{rule}'.");

                if (pre != attributeName)
                {
                    _logger?.LogDebug("Attribute '{0}' renamed to '{1}'", pre, attributeName);
                    return attributeName;
                }
            }

            return attributeName;
        }

        public class JobCompletionResult
        {
            public bool Success { get; set; }
            public string ErrorMessage { get; set; } = string.Empty;
        }

        private async Task<JobCompletionResult> WaitForJobCompletionViaPollingAsync(string uri, string jobId)
        {
            var result = new JobCompletionResult();

            try
            {
                _logger?.LogInformation("Polling for job {JobId} status", jobId);

                // Set up a timeout - 15 minutes should be enough for most jobs
                var timeout = TimeSpan.FromMinutes(15);
                var startTime = DateTime.UtcNow;
                var endTime = startTime.Add(timeout);

                // Poll interval - start with 2 seconds, then increase
                var pollInterval = TimeSpan.FromSeconds(2);
                var maxPollInterval = TimeSpan.FromSeconds(10);

                // Wait 3 seconds before the first poll
                _logger?.LogDebug("Waiting 3 seconds before first poll for job {JobId}", jobId);
                await Task.Delay(TimeSpan.FromSeconds(3));

                // Poll for job status until it completes or times out
                while (DateTime.UtcNow < endTime)
                {
                    _logger?.LogInformation("Checking status of job {JobId}", jobId);
                    string jobStatusJson = JsonActions.JsonReturnString(uri + $"/api/v2/analytics/conversations/details/jobs/{jobId}", GCApiKey);

                    if (string.IsNullOrWhiteSpace(jobStatusJson))
                    {
                        // This could be a 202 response (job still processing) - check the response code
                        if (JsonActions.responseCode == "Accepted")
                        {
                            var elapsed = DateTime.UtcNow - startTime;
                            _logger?.LogInformation("Job {JobId} still processing (HTTP 202 Accepted) - elapsed: {Elapsed:mm\\:ss}, next check in {Interval:F1}s",
                                jobId, elapsed, pollInterval.TotalSeconds);
                        }
                        else
                        {
                            _logger?.LogWarning("Empty response when checking job {JobId} status - response code: {ResponseCode}", jobId, JsonActions.responseCode ?? "Unknown");
                        }
                        await Task.Delay(pollInterval);
                        continue;
                    }

                    if (jobStatusJson.Contains("\"error\": true"))
                    {
                        _logger?.LogWarning("Error response when checking job {JobId} status: {Response}", jobId, jobStatusJson);
                        await Task.Delay(pollInterval);
                        continue;
                    }

                    var jobStatus = JsonConvert.DeserializeObject<DetInt.ReportJobStatus>(jobStatusJson,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });

                    if (jobStatus == null)
                    {
                        _logger?.LogWarning("Failed to deserialize job status for job {JobId}", jobId);
                        await Task.Delay(pollInterval);
                        continue;
                    }

                    _logger?.LogInformation("Job {JobId} status: {State}", jobId, jobStatus.state);

                    // Check if the job has completed
                    if (jobStatus.state == "FULFILLED" || jobStatus.state == "SUCCESS" || jobStatus.state == "COMPLETE")
                    {
                        _logger?.LogInformation("Job {JobId} completed successfully with state: {State}", jobId, jobStatus.state);
                        result.Success = true;
                        return result;
                    }

                    // Check if the job has failed
                    if (jobStatus.state == "FAILED" || jobStatus.state == "ERROR")
                    {
                        string errorMessage = $"Job failed with state: {jobStatus.state}";

                        _logger?.LogError("Job {JobId} failed: {ErrorMessage}", jobId, errorMessage);
                        result.ErrorMessage = errorMessage;
                        return result;
                    }

                    // Job is still running, wait and check again
                    _logger?.LogInformation("Job {JobId} is still running with state: {State}, checking again in {Interval} seconds",
                        jobId, jobStatus.state, pollInterval.TotalSeconds);

                    await Task.Delay(pollInterval);

                    // Increase poll interval for next iteration (up to max)
                    pollInterval = TimeSpan.FromMilliseconds(Math.Min(pollInterval.TotalMilliseconds * 1.5, maxPollInterval.TotalMilliseconds));
                }

                // If we get here, the job has timed out
                result.ErrorMessage = $"Timeout waiting for job to complete after {timeout.TotalMinutes} minutes";
                _logger?.LogError("Timeout waiting for job {JobId} to complete after {Timeout} minutes", jobId, timeout.TotalMinutes);
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"Error waiting for job completion: {ex.Message}";
                _logger?.LogError(ex, "Error waiting for job {JobId} completion", jobId);
            }

            return result;
        }

        public DataSet GetDetailInteractionDataFromGC(string SyncType, String StartDate, String EndDate)
        {
            return GetDetailInteractionDataFromGCAsync(SyncType, StartDate, EndDate).GetAwaiter().GetResult();
        }

        public DataSet GetDetailInteractionDataFromGC(string SyncType, String StartDate, String EndDate, TimeSpan? lookBackSpan)
        {
            return GetDetailInteractionDataFromGCAsync(SyncType, StartDate, EndDate, lookBackSpan).GetAwaiter().GetResult();
        }

        private async Task<DataSet> GetDetailInteractionDataFromGCAsync(string SyncType, String StartDate, String EndDate, TimeSpan? lookBackSpan = null)
        {
            _logger?.LogInformation("Initiating data retrieval job for sync type '{SyncType}' from {StartDate} to {EndDate}", SyncType, StartDate, EndDate);
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString() ?? "";
            string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/analytics/conversations/details/jobs/availability", GCApiKey);

            JobDateLimit DateMax;
            if (string.IsNullOrEmpty(JsonString))
            {
                _logger?.LogWarning("Failed to retrieve data availability information from API, using current time as fallback");
                DateMax = new JobDateLimit { dataAvailabilityDate = DateTime.UtcNow };
            }
            else
            {
                try
                {
                    DateMax = JsonConvert.DeserializeObject<JobDateLimit>(JsonString,
                                             new JsonSerializerSettings
                                             {
                                                 NullValueHandling = NullValueHandling.Ignore
                                             }) ?? new JobDateLimit { dataAvailabilityDate = DateTime.UtcNow };
                }
                catch (JsonException ex)
                {
                    _logger?.LogError(ex, "Failed to deserialize data availability response, using current time as fallback. Response: {Response}",
                        JsonString.Length > 200 ? JsonString.Substring(0, 200) + "..." : JsonString);
                    DateMax = new JobDateLimit { dataAvailabilityDate = DateTime.UtcNow };
                }
            }

            // Parse with DateTimeStyles.AdjustToUniversal to properly handle the 'Z' suffix without double conversion
            DateTime FromDate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.AdjustToUniversal);
            DateTime ToDate = DateTime.ParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fffZ", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.AdjustToUniversal);

            // Get timezone info for better logging
            TimeZoneInfo appTimeZone = null;
            try
            {
                appTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig ?? "UTC");
            }
            catch
            {
                appTimeZone = TimeZoneInfo.Utc;
            }

            _logger?.LogInformation("Data fetch parameters for sync type '{SyncType}':", SyncType);
            _logger?.LogInformation("- Start date (UTC): {StartDate}", StartDate);
            _logger?.LogInformation("- End date (UTC): {EndDate}", EndDate);
            _logger?.LogInformation("- From date (UTC): {FromDate:yyyy-MM-dd HH:mm:ss} | Local: {FromDateLocal:yyyy-MM-dd HH:mm:ss}",
                FromDate, TimeZoneInfo.ConvertTimeFromUtc(FromDate, appTimeZone));
            _logger?.LogInformation("- To date (UTC): {ToDate:yyyy-MM-dd HH:mm:ss} | Local: {ToDateLocal:yyyy-MM-dd HH:mm:ss}",
                ToDate, TimeZoneInfo.ConvertTimeFromUtc(ToDate, appTimeZone));
            _logger?.LogInformation("- Data availability date (UTC): {DataAvailabilityDate:yyyy-MM-dd HH:mm:ss} | Local: {DataAvailabilityDateLocal:yyyy-MM-dd HH:mm:ss}",
                DateMax.dataAvailabilityDate, TimeZoneInfo.ConvertTimeFromUtc(DateMax.dataAvailabilityDate, appTimeZone));
            _logger?.LogInformation("- Current time (UTC): {CurrentTime:yyyy-MM-dd HH:mm:ss} | Local: {CurrentTimeLocal:yyyy-MM-dd HH:mm:ss}",
                DateTime.UtcNow, TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, appTimeZone));
            _logger?.LogInformation("- Using timezone: {TimeZone}", appTimeZone.Id);

            bool RunJob = false;
            bool RunQuery = false;

            // Check if SyncType is explicitly set to force a specific mode
            if (SyncType?.ToUpper() == "JOB")
            {
                _logger?.LogInformation("SyncType explicitly set to JOB - forcing job mode regardless of data availability");
                RunJob = true;
                RunQuery = false;
            }
            else if (SyncType?.ToUpper() == "QUERY")
            {
                _logger?.LogInformation("SyncType explicitly set to QUERY - forcing query mode");
                RunJob = false;
                RunQuery = true;
            }
            else
            {
                // Original logic based on data availability
                if (FromDate < DateMax.dataAvailabilityDate)
                {
                    _logger?.LogInformation("Initiating job to retrieve complete or partial data set");
                    RunJob = true;

                    if (ToDate < DateMax.dataAvailabilityDate)
                    {
                        _logger?.LogInformation("End date is within data availability window - can retrieve all data with a single job");
                        RunQuery = false;
                    }
                    else
                    {
                        _logger?.LogInformation("End date exceeds data availability window - adjusting end date and using partial query for remaining data");
                        RunQuery = true;
                        ToDate = DateMax.dataAvailabilityDate;
                    }
                }
                else
                {
                    RunJob = false;
                    RunQuery = true;
                }
            }

            DataSet DSDetailInteraction = new DataSet();

            if (RunJob)
            {
                _logger?.LogInformation("Executing data retrieval job");
                DSDetailInteraction = await GetDetailInteractionDataFromGCJob(
                    FromDate.ToString("yyyy-MM-ddTHH:mm:00.000Z"),
                    ToDate.ToString("yyyy-MM-ddTHH:mm:00.000Z"));

                if (RunQuery)
                {
                    DateTime maxDate = DateMax.dataAvailabilityDate;
                    string queryStartDate = maxDate.ToString("yyyy-MM-ddTHH:mm:00.000Z");


                    DataSet TempDataSet = GetDetailInteractionDataFromGCQuery(queryStartDate, EndDate);
                    DataTable TempAttribs = GetParticipantAttributes(TempDataSet.Tables[0]);
                    MergeDataSets(DSDetailInteraction, TempDataSet, TempAttribs);
                }
            }
            else
            {
                DataSet TempDataSet = GetDetailInteractionDataFromGCQuery(StartDate, EndDate);
                DataTable TempAttribs = GetParticipantAttributes(TempDataSet.Tables[0]);

                // Check if tables already exist before adding to prevent DuplicateNameException
                var detailTable = TempDataSet.Tables[0].Copy();
                if (!DSDetailInteraction.Tables.Contains(detailTable.TableName))
                    DSDetailInteraction.Tables.Add(detailTable);
                else
                    _logger?.LogDebug("Table {TableName} already exists in DataSet, skipping addition", detailTable.TableName);

                if (!DSDetailInteraction.Tables.Contains(TempAttribs.TableName))
                    DSDetailInteraction.Tables.Add(TempAttribs);
                else
                    _logger?.LogDebug("Table {TableName} already exists in DataSet, skipping addition", TempAttribs.TableName);

                var participantSummaryTable = TempDataSet.Tables[1].Copy();
                if (!DSDetailInteraction.Tables.Contains(participantSummaryTable.TableName))
                    DSDetailInteraction.Tables.Add(participantSummaryTable);
                else
                    _logger?.LogDebug("Table {TableName} already exists in DataSet, skipping addition", participantSummaryTable.TableName);

                var flowOutcomesTable = TempDataSet.Tables[2].Copy();
                if (!DSDetailInteraction.Tables.Contains(flowOutcomesTable.TableName))
                    DSDetailInteraction.Tables.Add(flowOutcomesTable);
                else
                    _logger?.LogDebug("Table {TableName} already exists in DataSet, skipping addition", flowOutcomesTable.TableName);
            }

            // FIXED: Prevent double processing by excluding conversations already processed in the main Job/Query flows
            // Outstanding conversations should only include those that started BEFORE the current processing date range
            // but are still ongoing (haven't ended yet)
            string outstandingQuery;
            if (RunJob || RunQuery)
            {
                // If we processed conversations from a specific date range, exclude those from outstanding processing
                // Only process outstanding conversations that started before our processing window
                outstandingQuery = $@"
                    select conversationid
                    from convsummarydata
                    where conversationenddateltc is null
                    and firstmediatype = 'voice'
                    and conversationstartdate < '{FromDate:yyyy-MM-ddTHH:mm:ss.fff}'";

                _logger?.LogInformation("Outstanding conversations query: Excluding conversations that started after {FromDate} to prevent double processing", FromDate);
            }
            else
            {
                // If we didn't process any specific date range, process all outstanding conversations
                outstandingQuery = "select conversationid from convsummarydata where conversationenddateltc is null and firstmediatype = 'voice'";
                _logger?.LogInformation("Outstanding conversations query: Processing all outstanding conversations (no date range exclusion needed)");
            }

            DataTable OutConversations = DBUtil.GetSQLTableData(outstandingQuery, "ouytconversations");
            _logger?.LogInformation("Found {OutstandingCount} outstanding voice conversations to process (after duplicate prevention)", OutConversations.Rows.Count);

            if (OutConversations.Rows.Count > 0)
            {
                _logger?.LogInformation("Processing outstanding conversations (no duplicates from main processing)");
                DataSet TempDataSet = await GetOutStandingConversations(OutConversations, lookBackSpan);
                DataTable TempAttribs = GetParticipantAttributes(TempDataSet.Tables[0]);
                MergeDataSets(DSDetailInteraction, TempDataSet, TempAttribs);
            }

            // Data processing completed
            if (DSDetailInteraction.Tables.Count > 0)
            {
                DSDetailInteraction.Tables.Add(GetInteractionSummary(DSDetailInteraction.Tables[0]));
            }

            _logger?.LogInformation("Data retrieval completed, returning {TableCount} tables to calling method", DSDetailInteraction.Tables.Count);
            return DSDetailInteraction;
        }

        // Add lock object for thread-safe DataTable operations in MergeDataSets
        private static readonly object _mergeDataSetsLock = new object();

        private void MergeDataSets(DataSet DSDetailInteraction, DataSet TempDataSet, DataTable? TempAttribs)
        {
            // Check if both datasets have the required tables
            if (DSDetailInteraction.Tables.Count < 1 || TempDataSet.Tables.Count < 1)
            {
                _logger?.LogWarning("One of the datasets doesn't have enough tables for merging. DSDetailInteraction has {DSDetailInteractionTableCount} tables, TempDataSet has {TempDataSetTableCount} tables",
                    DSDetailInteraction.Tables.Count, TempDataSet.Tables.Count);
                return;
            }

            // Process the first table (detailed interaction data)
            int addedInteractionCount = 0, updatedInteractionCount = 0, constraintErrorCount = 0, otherErrorCount = 0;

            foreach (DataRow TempInt in TempDataSet.Tables[0].Rows)
            {
                try
                {
                    string keyId = TempInt["keyid"]?.ToString() ?? "";

                    // PERFORMANCE OPTIMIZATION: Use HashSet lookup instead of DataTable.Select (40-60% faster)
                    lock (_keyCacheLock)
                    {
                        if (!_detailInteractionKeyCache.Contains(keyId))
                        {
                            DSDetailInteraction.Tables[0].ImportRow(TempInt);
                            _detailInteractionKeyCache.Add(keyId);
                            addedInteractionCount++;
                            // _logger?.LogDebug("Added new interaction record with keyid: {KeyId}", keyId);
                        }
                        else
                        {
                            // Find existing row for update (fallback to Select for updates only)
                            DataRow DetailedRowTemp = DSDetailInteraction.Tables[0].Select("keyid = '" + keyId + "'").FirstOrDefault();
                        foreach (DataColumn DetailedColumn in DSDetailInteraction.Tables[0].Columns)
                        {
                            if (!DetailedColumn.ReadOnly && DetailedRowTemp[DetailedColumn.ColumnName].ToString() != TempInt[DetailedColumn.ColumnName].ToString())
                                DetailedRowTemp[DetailedColumn.ColumnName] = TempInt[DetailedColumn.ColumnName];
                        }
                        updatedInteractionCount++;
                        // _logger?.LogDebug("Updated existing interaction record with keyid: {KeyId}", TempInt["keyid"]);
                        }
                    }
                }
                catch (System.Data.ConstraintException ex)
                {
                    constraintErrorCount++;
                    // Suppress individual constraint violation debug messages to reduce log noise
                    // These are expected when the same interaction data is processed multiple times
                }
                catch (Exception e)
                {
                    otherErrorCount++;
                    string conversationId = TempInt["conversationid"]?.ToString() ?? TempInt["keyid"]?.ToString() ?? "Unknown";
                    _logger?.LogError(e, "Unexpected exception in Detailed Interaction Merge for conversation {ConversationId}", conversationId);
                }
            }

            // Summary log for interaction data processing
            if (addedInteractionCount > 0 || updatedInteractionCount > 0 || constraintErrorCount > 0 || otherErrorCount > 0)
            {
                _logger?.LogInformation("Interaction data merge: {Added} added, {Updated} updated, {ConstraintErrors} duplicate key conflicts (suppressed), {OtherErrors} other errors",
                    addedInteractionCount, updatedInteractionCount, constraintErrorCount, otherErrorCount);
            }

            // Process participant attributes if available
            // Find the participant attributes table in the main DataSet by name to avoid index-based assumptions
            DataTable participantAttributesTable = null;
            foreach (DataTable table in DSDetailInteraction.Tables)
            {
                if (table.TableName == "participantAttributesDynamic")
                {
                    participantAttributesTable = table;
                    break;
                }
            }

            if (TempAttribs != null && participantAttributesTable != null)
            {
                DataColumnCollection columns = participantAttributesTable.Columns;
                foreach (DataColumn AttribCol in TempAttribs.Columns)
                {
                    if (!columns.Contains(AttribCol.ColumnName))
                    {
                        participantAttributesTable.Columns.Add(AttribCol.ColumnName, AttribCol.DataType);
                        _logger?.LogDebug("Added new attribute column: {ColumnName}", AttribCol.ColumnName);
                    }
                }

                int addedCount = 0, updatedCount = 0, participantConstraintErrorCount = 0, participantOtherErrorCount = 0;

                foreach (DataRow TempAttrib in TempAttribs.Rows)
                {
                    try
                    {
                        // Use lock for thread-safe DataTable operations
                        lock (_mergeDataSetsLock)
                        {
                            string keyId = TempAttrib["keyid"]?.ToString() ?? "";

                            // PERFORMANCE OPTIMIZATION: Use HashSet lookup instead of DataTable.Select
                            if (!_participantAttributesKeyCache.Contains(keyId))
                            {
                                participantAttributesTable.ImportRow(TempAttrib);
                                _participantAttributesKeyCache.Add(keyId);
                                addedCount++;
                            }
                            else
                            {
                                // Find existing row for update (fallback to Select for updates only)
                                DataRow DetailedRowTemp = participantAttributesTable.Select("keyid = '" + keyId + "'").FirstOrDefault();
                                foreach (DataColumn DetailedColumn in participantAttributesTable.Columns)
                                {
                                    // Check if the column exists in the source table before accessing it
                                    if (!DetailedColumn.ReadOnly && TempAttrib.Table.Columns.Contains(DetailedColumn.ColumnName))
                                    {
                                        if (DetailedRowTemp[DetailedColumn.ColumnName].ToString() != TempAttrib[DetailedColumn.ColumnName].ToString())
                                            DetailedRowTemp[DetailedColumn.ColumnName] = TempAttrib[DetailedColumn.ColumnName];
                                    }
                                }
                                updatedCount++;
                            }
                        }
                    }
                    catch (System.Data.ConstraintException ex)
                    {
                        participantConstraintErrorCount++;
                        // Suppress individual constraint violation debug messages to reduce log noise
                        // These are expected when the same participant attributes are processed multiple times
                    }
                    catch (Exception e)
                    {
                        participantOtherErrorCount++;
                        _logger?.LogError(e, "Exception caught in participant attributes merge module for keyid {KeyId}: {ErrorMessage}",
                            TempAttrib["keyid"], e.Message);
                    }
                }

                // Smart summary log instead of repetitive ATD: entries
                if (addedCount > 0 || updatedCount > 0 || participantConstraintErrorCount > 0 || participantOtherErrorCount > 0)
                {
                    _logger?.LogInformation("Participant attributes merge: {Added} added, {Updated} updated, {ConstraintErrors} duplicate key conflicts (suppressed), {OtherErrors} other errors",
                        addedCount, updatedCount, participantConstraintErrorCount, participantOtherErrorCount);
                    // Participant attributes merge completed
                }
            }
            else if (TempAttribs != null && participantAttributesTable == null)
            {
                // If we don't have a participant attributes table yet, add the TempAttribs table to the DataSet
                // But first check if a table with this name already exists to prevent DuplicateNameException
                if (!DSDetailInteraction.Tables.Contains(TempAttribs.TableName))
                {
                    DSDetailInteraction.Tables.Add(TempAttribs.Copy());
                    _logger?.LogDebug("Added new participant attributes table {TableName} to DataSet", TempAttribs.TableName);
                }
                else
                {
                    _logger?.LogDebug("Table {TableName} already exists in DataSet, skipping addition", TempAttribs.TableName);
                }
            }

            // Process participant summary data if available
            // Find the participant summary table in the main DataSet by name to avoid index-based assumptions
            DataTable participantSummaryTable = null;
            foreach (DataTable table in DSDetailInteraction.Tables)
            {
                if (table.TableName == "participantsummaryData")
                {
                    participantSummaryTable = table;
                    break;
                }
            }

            if (TempDataSet.Tables.Count >= 2 && participantSummaryTable != null)
            {
                int addedCount = 0, updatedCount = 0, errorCount = 0;

                foreach (DataRow TempPartSumm in TempDataSet.Tables[1].Rows)
                {
                    try
                    {
                        // Use lock for thread-safe DataTable operations to prevent PURPOSE null errors
                        lock (_mergeDataSetsLock)
                        {
                            string keyId = TempPartSumm["keyid"]?.ToString() ?? "";

                            // PERFORMANCE OPTIMIZATION: Use HashSet lookup instead of DataTable.Select
                            if (!_participantSummaryKeyCache.Contains(keyId))
                            {
                                participantSummaryTable.ImportRow(TempPartSumm);
                                _participantSummaryKeyCache.Add(keyId);
                                addedCount++;
                            }
                            else
                            {
                                // Find existing row for update (fallback to Select for updates only)
                                DataRow DetailedRowTemp = participantSummaryTable.Select("keyid = '" + keyId + "'").FirstOrDefault();
                                foreach (DataColumn DetailedColumn in participantSummaryTable.Columns)
                                {
                                    // Check if the column exists in the source table before accessing it
                                    if (!DetailedColumn.ReadOnly && TempPartSumm.Table.Columns.Contains(DetailedColumn.ColumnName))
                                    {
                                        if (DetailedRowTemp[DetailedColumn.ColumnName].ToString() != TempPartSumm[DetailedColumn.ColumnName].ToString())
                                            DetailedRowTemp[DetailedColumn.ColumnName] = TempPartSumm[DetailedColumn.ColumnName];
                                    }
                                }
                                updatedCount++;
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        errorCount++;

                        // Handle PURPOSE column null errors with more meaningful logging
                        if (e is System.Data.NoNullAllowedException && e.Message.Contains("PURPOSE"))
                        {
                            // Extract purpose value from the source data for meaningful logging
                            string purposeValue = "null";
                            try
                            {
                                if (TempPartSumm.Table.Columns.Contains("purpose") && TempPartSumm["purpose"] != null && TempPartSumm["purpose"] != DBNull.Value)
                                {
                                    purposeValue = TempPartSumm["purpose"].ToString();
                                }
                            }
                            catch
                            {
                                purposeValue = "unable_to_read";
                            }

                            _logger?.LogWarning("Participant summary merge: Skipping row with null PURPOSE field for keyid {KeyId}. Purpose value from JSON: '{PurposeValue}'. This is expected for some conversation types.",
                                TempPartSumm["keyid"], purposeValue);
                        }
                        else
                        {
                            _logger?.LogError(e, "Exception caught in participant summary merge module for keyid {KeyId}: {ErrorMessage}",
                                TempPartSumm["keyid"], e.Message);
                        }
                    }
                }

                // Smart summary log instead of repetitive PSA: entries
                if (addedCount > 0 || updatedCount > 0 || errorCount > 0)
                {
                    _logger?.LogDebug("Participant summary merge: {Added} added, {Updated} updated, {Errors} errors",
                        addedCount, updatedCount, errorCount);
                    // Participant summary merge completed
                }
            }

            // Process flow outcome data if available
            // Find the flow outcomes table in the main DataSet by name to avoid index-based assumptions
            DataTable flowOutcomesTable = null;
            foreach (DataTable table in DSDetailInteraction.Tables)
            {
                if (table.TableName == "flowoutcomedata")
                {
                    flowOutcomesTable = table;
                    break;
                }
            }

            if (TempDataSet.Tables.Count >= 3 && flowOutcomesTable != null)
            {
                int addedCount = 0, updatedCount = 0, errorCount = 0;

                foreach (DataRow TempFlowOutcome in TempDataSet.Tables[2].Rows)
                {
                    try
                    {
                        // Use lock for thread-safe DataTable operations
                        lock (_mergeDataSetsLock)
                        {
                            string keyId = TempFlowOutcome["keyid"]?.ToString() ?? "";

                            // PERFORMANCE OPTIMIZATION: Use HashSet lookup instead of DataTable.Select
                            if (!_flowOutcomesKeyCache.Contains(keyId))
                            {
                                flowOutcomesTable.ImportRow(TempFlowOutcome);
                                _flowOutcomesKeyCache.Add(keyId);
                                addedCount++;
                            }
                            else
                            {
                                // Find existing row for update (fallback to Select for updates only)
                                DataRow DetailedRowTemp = flowOutcomesTable.Select("keyid = '" + keyId + "'").FirstOrDefault();
                                foreach (DataColumn DetailedColumn in flowOutcomesTable.Columns)
                                {
                                    // Check if the column exists in the source table before accessing it
                                    if (!DetailedColumn.ReadOnly && TempFlowOutcome.Table.Columns.Contains(DetailedColumn.ColumnName))
                                    {
                                        if (DetailedRowTemp[DetailedColumn.ColumnName].ToString() != TempFlowOutcome[DetailedColumn.ColumnName].ToString())
                                            DetailedRowTemp[DetailedColumn.ColumnName] = TempFlowOutcome[DetailedColumn.ColumnName];
                                    }
                                }
                                updatedCount++;
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        errorCount++;
                        _logger?.LogError(e, "Exception caught in flow outcome merge module for keyid {KeyId}: {ErrorMessage}",
                            TempFlowOutcome["keyid"], e.Message);
                    }
                }

                // Log flow outcome merge results
                if (addedCount > 0 || updatedCount > 0 || errorCount > 0)
                {
                    if (errorCount > 0)
                    {
                        _logger?.LogWarning("Flow outcome merge completed: {Added} added, {Updated} updated, {Errors} errors",
                            addedCount, updatedCount, errorCount);
                    }
                    else
                    {
                        _logger?.LogInformation("Flow outcome merge completed: {Added} added, {Updated} updated",
                            addedCount, updatedCount);
                    }
                }
            }


        }

        private async Task<DataSet> GetOutStandingConversations(DataTable OutConversations, TimeSpan? lookBackSpan = null)
        {
            _logger?.LogInformation("Processing {ConversationCount} outstanding conversations", OutConversations.Rows.Count);

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            _logger?.LogDebug("Creating memory tables for outstanding conversations processing");
            DataTable DetailInteraction = DBUtil.CreateInMemTable("detailedInteractionData");
            DataTable ParticipantSummary = DBUtil.CreateInMemTable("participantsummaryData");
            DataTable FlowOutcomes = DBUtil.CreateInMemTable("flowoutcomedata");

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            int MaxRowsToSend = 75;
            int currentPage = 1;

            int totalPages = (OutConversations.Rows.Count % MaxRowsToSend == 0) ?
                (OutConversations.Rows.Count / MaxRowsToSend) :
                (OutConversations.Rows.Count / MaxRowsToSend) + 1;

            while (currentPage <= totalPages)
            {
                _logger?.LogDebug("Processing outstanding conversations batch page {Page} of {TotalPages}", currentPage, totalPages);
                StringBuilder SelectString = new StringBuilder("");
                DataTable dtTemp = OutConversations.Rows.Cast<System.Data.DataRow>().Skip((currentPage - 1) * MaxRowsToSend).Take(MaxRowsToSend).CopyToDataTable();
                dtTemp.TableName = OutConversations.TableName;

                foreach (DataRow DRRow in dtTemp.Rows)
                {
                    // Processing conversation - using debug level to avoid log flooding
                    SelectString.Append(DRRow["conversationid"] + ",");
                }

                SelectString.Length = SelectString.Length - 1;

                // Processing outstanding conversations batch

                // Refresh API token at configured interval to prevent rate limiting
                if ((currentPage % _tokenRefreshInterval) == 0)
                {
                    _logger?.LogDebug("Processing outstanding conversations page {Page}, refreshing API key", currentPage);
                    bool getnewAPIKEY = GCUtilities.GetGCAPIKey();
                    GCApiKey = GCUtilities.GCApiKey;
                }

                // Use centralized rate limiting handler for outstanding conversations with robust retry logic
                string JsonString = null;
                int retryAttempts = 0;
                const int maxRetryAttempts = 3;
                bool shouldRetry = true;

                while (shouldRetry && retryAttempts <= maxRetryAttempts)
                {
                    try
                    {
                        string apiUrl = URI + "/api/v2/analytics/conversations/details?id=" + SelectString.ToString();
                        _logger?.LogDebug("Attempting to retrieve outstanding conversations batch on page {Page}, attempt {Attempt}/{MaxAttempts}. URI: {URI}",
                            currentPage, retryAttempts + 1, maxRetryAttempts + 1, apiUrl);

                        // Use the centralized JsonReturnString method which has proper rate limiting
                        JsonString = JsonActions.JsonReturnString(apiUrl, GCApiKey);

                        // Handle different response scenarios
                        if (string.IsNullOrEmpty(JsonString))
                        {
                            if (retryAttempts < maxRetryAttempts)
                            {
                                retryAttempts++;
                                int delayMs = (int)Math.Pow(2, retryAttempts) * 1000; // Exponential backoff: 2s, 4s, 8s
                                _logger?.LogWarning("Empty response from outstanding conversations API for page {Page}, attempt {Attempt}/{MaxAttempts}. Retrying in {DelayMs}ms. URI: {URI}",
                                    currentPage, retryAttempts, maxRetryAttempts + 1, delayMs, apiUrl);
                                await Task.Delay(delayMs);
                                continue;
                            }
                            else
                            {
                                _logger?.LogError("Empty response from outstanding conversations API for page {Page} after {MaxAttempts} attempts. This may indicate no conversations to process or a persistent API issue. URI: {URI}",
                                    currentPage, maxRetryAttempts + 1, apiUrl);

                                // Check if this is a legitimate empty result (no conversations to process)
                                // If we're processing outstanding conversations and get empty results, it might be legitimate
                                _logger?.LogInformation("Treating empty response as no outstanding conversations to process for page {Page}. Continuing to next page.", currentPage);
                                shouldRetry = false;
                                break; // Exit the retry loop and continue to next page
                            }
                        }

                        // Check for structured error responses from the centralized handler
                        if (!string.IsNullOrEmpty(JsonString) && JsonString.Contains("\"error\": true"))
                        {
                            // Parse the error response to determine if it's retryable
                            bool isRetryableError = false;
                            string errorType = "Unknown";

                            try
                            {
                                var errorObj = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(JsonString);
                                if (errorObj?.statusCode != null)
                                {
                                    errorType = errorObj.statusCode.ToString();

                                    // Determine if this is a retryable error
                                    isRetryableError = errorType == "TooManyRequests" ||
                                                     errorType == "RequestTimeout" ||
                                                     errorType == "InternalServerError" ||
                                                     errorType == "BadGateway" ||
                                                     errorType == "ServiceUnavailable" ||
                                                     errorType == "GatewayTimeout";
                                }
                            }
                            catch (Exception parseEx)
                            {
                                _logger?.LogWarning(parseEx, "Failed to parse error response JSON for page {Page}: {Response}", currentPage, JsonString);
                            }

                            if (isRetryableError && retryAttempts < maxRetryAttempts)
                            {
                                retryAttempts++;
                                int delayMs = (int)Math.Pow(2, retryAttempts) * 1000; // Exponential backoff
                                _logger?.LogWarning("Retryable error ({ErrorType}) from outstanding conversations API for page {Page}, attempt {Attempt}/{MaxAttempts}. Retrying in {DelayMs}ms. Response: {Response}",
                                    errorType, currentPage, retryAttempts, maxRetryAttempts + 1, delayMs, JsonString.Length > 200 ? JsonString.Substring(0, 200) + "..." : JsonString);
                                await Task.Delay(delayMs);
                                continue;
                            }
                            else
                            {
                                _logger?.LogError("Non-retryable error or max retries exceeded for outstanding conversations API on page {Page}. ErrorType: {ErrorType}, Attempts: {Attempts}. Response: {Response}",
                                    currentPage, errorType, retryAttempts + 1, JsonString.Length > 500 ? JsonString.Substring(0, 500) + "..." : JsonString);
                                throw new InvalidOperationException($"API error for outstanding conversations batch on page {currentPage} after {retryAttempts + 1} attempts. ErrorType: {errorType}. Response: {JsonString.Substring(0, Math.Min(JsonString.Length, 500))}");
                            }
                        }

                        // If we get here, we have a successful response
                        shouldRetry = false;
                        _logger?.LogDebug("Successfully retrieved outstanding conversations batch on page {Page} after {Attempts} attempt(s)", currentPage, retryAttempts + 1);
                    }
                    catch (Exception ex)
                    {
                        if (retryAttempts < maxRetryAttempts && !(ex is UnauthorizedAccessException))
                        {
                            retryAttempts++;
                            int delayMs = (int)Math.Pow(2, retryAttempts) * 1000; // Exponential backoff
                            _logger?.LogWarning(ex, "Exception retrieving outstanding conversations batch on page {Page}, attempt {Attempt}/{MaxAttempts}. Retrying in {DelayMs}ms.",
                                currentPage, retryAttempts, maxRetryAttempts + 1, delayMs);
                            await Task.Delay(delayMs);
                            continue;
                        }
                        else
                        {
                            _logger?.LogError(ex, "Failed to retrieve outstanding conversations batch on page {Page} after {MaxAttempts} attempts. Exception type: {ExceptionType}",
                                currentPage, maxRetryAttempts + 1, ex.GetType().Name);
                            throw;
                        }
                    }
                }

                // Handle the case where we have no data to process (empty response after retries)
                if (string.IsNullOrEmpty(JsonString))
                {
                    _logger?.LogInformation("No outstanding conversations data to process for page {Page}. This may be normal if there are no outstanding conversations.", currentPage);
                    currentPage++;
                    continue; // Skip to next page
                }

                // Validate that we have meaningful JSON data to process
                if (JsonString.Length > 50)
                {
                    // Additional validation to ensure we have valid conversation data, not error JSON
                    if (JsonString.Contains("\"error\"") && JsonString.Contains("\"message\""))
                    {
                        _logger?.LogError("API returned error JSON instead of conversation data for outstanding conversations batch on page {Page}. Response: {Response}",
                            currentPage, JsonString.Length > 500 ? JsonString.Substring(0, 500) + "..." : JsonString);
                        throw new InvalidOperationException($"API returned error JSON instead of conversation data for outstanding conversations batch on page {currentPage}. Response: {JsonString.Substring(0, Math.Min(JsonString.Length, 500))}");
                    }

                    // Validate that the JSON looks like conversation data
                    if (!JsonString.Contains("conversations") && !JsonString.Contains("\"totalHits\""))
                    {
                        _logger?.LogWarning("API response does not contain expected conversation data structure for page {Page}. Response preview: {ResponsePreview}",
                            currentPage, JsonString.Length > 200 ? JsonString.Substring(0, 200) + "..." : JsonString);

                        // This might be a legitimate empty result, so don't throw an exception
                        _logger?.LogInformation("Treating unexpected response structure as no conversations to process for page {Page}. Continuing to next page.", currentPage);
                        currentPage++;
                        continue;
                    }

                    _logger?.LogInformation("Interactions To Process");
                    Interactions.InteractionSegmentStruct DetailedData = null;

                    try
                    {
                        DetailedData = JsonConvert.DeserializeObject<Interactions.InteractionSegmentStruct>(JsonString,
                                       new JsonSerializerSettings
                                       {
                                           NullValueHandling = NullValueHandling.Ignore,
                                           MissingMemberHandling = MissingMemberHandling.Ignore
                                       });
                    }
                    catch (JsonException jsonEx)
                    {
                        _logger?.LogError(jsonEx, "JSON deserialization failed for outstanding conversations batch on page {Page}. This may indicate an API response format change or corrupted data. JSON response preview: {JsonPreview}",
                            currentPage, JsonString.Length > 500 ? JsonString.Substring(0, 500) + "..." : JsonString);

                        // For outstanding conversations, if deserialization fails, we should continue to next page rather than fail completely
                        _logger?.LogWarning("Skipping page {Page} due to JSON deserialization failure. Continuing to next page to avoid blocking other conversations.", currentPage);
                        currentPage++;
                        continue;
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Unexpected error during JSON deserialization for outstanding conversations batch on page {Page}. Exception type: {ExceptionType}",
                            currentPage, ex.GetType().Name);

                        // For unexpected errors, also continue to next page
                        _logger?.LogWarning("Skipping page {Page} due to unexpected deserialization error. Continuing to next page.", currentPage);
                        currentPage++;
                        continue;
                    }

                    // Check if DetailedData and conversations are not null before processing
                    if (DetailedData == null)
                    {
                        _logger?.LogWarning("DetailedData is null after JSON deserialization for outstanding conversations batch on page {Page}. This may indicate an empty or invalid response. Continuing to next page.",
                            currentPage);
                        currentPage++;
                        continue;
                    }

                    if (DetailedData.conversations == null || DetailedData.conversations.Length == 0)
                    {
                        _logger?.LogInformation("No conversations found in response for outstanding conversations batch on page {Page}. This may be normal if there are no outstanding conversations to process.",
                            currentPage);
                        currentPage++;
                        continue;
                    }

                    bool timeFieldsMillisecondResolution = DetailInteraction.Columns["segmenttime"].DataType == typeof(System.Decimal);
                    int conversationCount = 0;
                    int totalConversationsInBatch = DetailedData.conversations.Length;

                    _logger?.LogInformation("Processing outstanding conversations batch on page {Page}: {ConversationCount} conversations found",
                        currentPage, totalConversationsInBatch);

                    foreach (Interactions.Conversation ConvData in DetailedData.conversations)
                    {
                        if (ConvData != null)
                        {
                            conversationCount++;
                            // Log progress every 50 conversations for better monitoring
                            if (conversationCount % 50 == 0)
                            {
                                _logger?.LogInformation("Outstanding conversations processing progress on page {Page}: {ProcessedCount}/{TotalCount} conversations processed",
                                    currentPage, conversationCount, totalConversationsInBatch);
                            }
                            int PartCode = 0;
                            int SessCode = 0;
                            int SegCode = 0;
                            // Add null safety check for participants collection
                            if (ConvData.participants == null)
                            {
                                _logger?.LogWarning("Participants collection is null for conversation {ConversationId}. Skipping participant processing.", ConvData.conversationId);
                                continue;
                            }

                            foreach (Interactions.Participant ConvPart in ConvData.participants)
                            {
                                // Add null safety check for individual participant
                                if (ConvPart == null)
                                {
                                    _logger?.LogWarning("Null participant found in conversation {ConversationId}. Skipping participant.", ConvData.conversationId);
                                    continue;
                                }

                                // Add null safety check for critical participant properties
                                if (string.IsNullOrEmpty(ConvPart.participantId))
                                {
                                    _logger?.LogWarning("Participant with null or empty participantId found in conversation {ConversationId}. Skipping participant.", ConvData.conversationId);
                                    continue;
                                }

                                DataRow CheckPartExists = ParticipantSummary.Select("keyid= '" + ConvData.conversationId + "|" + ConvPart.participantId + "'").FirstOrDefault();

                                DataRow DRPartSumm = ParticipantSummary.NewRow();
                                DRPartSumm["keyid"] = ConvData.conversationId + "|" + ConvPart.participantId;
                                DRPartSumm["conversationid"] = ConvData.conversationId;
                                DRPartSumm["participantid"] = ConvPart.participantId;
                                // Handle null purpose value to prevent NoNullAllowedException
                                DRPartSumm["purpose"] = !string.IsNullOrEmpty(ConvPart.purpose) ? ConvPart.purpose : "unknown";

                                if (ConvData.divisionIds.Length > 0 && !string.IsNullOrEmpty(ConvData.divisionIds[0]))
                                {
                                    DRPartSumm["divisionid"] = ConvData.divisionIds[0];
                                }
                                else
                                {
                                    DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                }

                                if (DRPartSumm["divisionid"] == null || DRPartSumm["divisionid"] == System.DBNull.Value || DRPartSumm["divisionid"] is DBNull)
                                {
                                    _logger?.LogDebug("Setting default division ID for participant summary");
                                    DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                }
                                if (ConvData.divisionIds.Length > 1 && !string.IsNullOrEmpty(ConvData.divisionIds[1]))
                                    DRPartSumm["divisionid2"] = ConvData.divisionIds[1];
                                if (ConvData.divisionIds.Length > 2 && !string.IsNullOrEmpty(ConvData.divisionIds[2]))
                                    DRPartSumm["divisionid3"] = ConvData.divisionIds[2];

                                // ConvData.conversationStart is already UTC from API deserialization
                                // Validate conversation start date - throw if invalid to maintain data integrity
                                if (ConvData.conversationStart == default(DateTime))
                                {
                                    throw new InvalidDataException($"Invalid conversation start date {ConvData.conversationStart} for conversation {ConvData.conversationId}. Cannot process conversation with invalid date.");
                                }

                                DRPartSumm["conversationstartdate"] = ConvData.conversationStart;
                                DRPartSumm["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvData.conversationStart, AppTimeZone);

                                if (ConvData.conversationEnd > DateTime.UtcNow.AddYears(-20))
                                {
                                    // ConvData.conversationEnd is already UTC from API deserialization
                                    DRPartSumm["conversationenddate"] = ConvData.conversationEnd;
                                    DRPartSumm["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvData.conversationEnd, AppTimeZone);
                                }
                                else
                                {
                                    DRPartSumm["conversationenddate"] = System.DBNull.Value;
                                    DRPartSumm["conversationenddateltc"] = System.DBNull.Value;
                                }

                                PartCode++;
                                SessCode = 0;
                                SegCode = 0;

                                // Add null safety check for sessions collection
                                if (ConvPart.sessions == null)
                                {
                                    _logger?.LogWarning("Sessions collection is null for participant {ParticipantId} in conversation {ConversationId}. Skipping session processing.",
                                        ConvPart.participantId, ConvData.conversationId);
                                    continue;
                                }

                                foreach (Interactions.Session ConvSess in ConvPart.sessions)
                                {
                                    // Add null safety check for individual session
                                    if (ConvSess == null)
                                    {
                                        _logger?.LogWarning("Null session found for participant {ParticipantId} in conversation {ConversationId}. Skipping session.",
                                            ConvPart.participantId, ConvData.conversationId);
                                        continue;
                                    }

                                    // Add null safety check for segments collection
                                    if (ConvSess.segments == null)
                                    {
                                        _logger?.LogWarning("Segments collection is null for session in participant {ParticipantId} in conversation {ConversationId}. Skipping segment processing.",
                                            ConvPart.participantId, ConvData.conversationId);
                                        continue;
                                    }

                                    SessCode++;
                                    SegCode = 0;
                                    Interactions.Flow ConvSessFlow = ConvSess.flow;



                                    int SegmentCount = ConvSess.segments.Length;
                                    int CurrentSegment = 1;

                                    foreach (Interactions.Segment ConvSeg in ConvSess.segments)
                                    {
                                        // Add null safety check for individual segment
                                        if (ConvSeg == null)
                                        {
                                            _logger?.LogWarning("Null segment found for session in participant {ParticipantId} in conversation {ConversationId}. Skipping segment.",
                                                ConvPart.participantId, ConvData.conversationId);
                                            continue;
                                        }

                                        SegCode++;

                                        if (!timeFieldsMillisecondResolution)
                                        {
                                            ConvSeg.segmentStart = new DateTime(
                                                ConvSeg.segmentStart.Ticks - (ConvSeg.segmentStart.Ticks % TimeSpan.TicksPerSecond),
                                                ConvSeg.segmentStart.Kind
                                            );
                                            ConvSeg.segmentEnd = new DateTime(
                                                ConvSeg.segmentEnd.Ticks - (ConvSeg.segmentEnd.Ticks % TimeSpan.TicksPerSecond),
                                                ConvSeg.segmentEnd.Kind
                                            );
                                        }

                                        string IterationCode = PartCode.ToString() + "|" + SessCode.ToString() + "|" + SegCode.ToString();
                                        DataRow NewRow = DetailInteraction.NewRow();
                                        string TempKeyid = ConvData.conversationId + "|" + IterationCode;
                                        NewRow["keyid"] = ConvData.conversationId + "|ID:" + UCAUtils.GetStableHashCode(TempKeyid);
                                        NewRow["conversationid"] = ConvData.conversationId;

                                        if (ConvData.divisionIds.Length > 0 && !string.IsNullOrEmpty(ConvData.divisionIds[0]))
                                        {
                                            NewRow["divisionid"] = ConvData.divisionIds[0];
                                        }
                                        else
                                        {
                                            NewRow["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                        }

                                        if (NewRow["divisionid"] == null || NewRow["divisionid"] == System.DBNull.Value|| NewRow["divisionid"] is DBNull)
                                        {
                                            _logger?.LogDebug("Setting default division ID for detailed interaction");
                                            NewRow["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                        }

                                        if (ConvData.divisionIds.Length > 1 && !string.IsNullOrEmpty(ConvData.divisionIds[1]))
                                            NewRow["divisionid2"] = ConvData.divisionIds[1];
                                        if (ConvData.divisionIds.Length > 2 && !string.IsNullOrEmpty(ConvData.divisionIds[2]))
                                            NewRow["divisionid3"] = ConvData.divisionIds[2];

                                        // ConvData.conversationStart is already UTC from API deserialization
                                        // Validate conversation start date - throw if invalid to maintain data integrity
                                        if (ConvData.conversationStart == default(DateTime))
                                        {
                                            throw new InvalidDataException($"Invalid conversation start date {ConvData.conversationStart} for conversation {ConvData.conversationId}. Cannot process conversation with invalid date.");
                                        }

                                        NewRow["conversationstartdate"] = ConvData.conversationStart;
                                        NewRow["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvData.conversationStart, AppTimeZone);

                                        if (ConvData.conversationEnd > DateTime.UtcNow.AddYears(-20))
                                        {
                                            // ConvData.conversationEnd is already UTC from API deserialization
                                            NewRow["conversationenddate"] = ConvData.conversationEnd;
                                            NewRow["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(ConvData.conversationEnd, AppTimeZone);
                                        }
                                        else
                                        {
                                            NewRow["conversationenddate"] = System.DBNull.Value;
                                            NewRow["conversationenddateltc"] = System.DBNull.Value;
                                        }

                                        NewRow["gencode"] = IterationCode;
                                        NewRow["peer"] = ConvSess.peerId;

                                        DateTime MaxDateTest = ConvData.conversationStart;
                                        if (MaxDateTest > DetailInteractionLastUpdate)
                                        {
                                            DetailInteractionLastUpdate = MaxDateTest;
                                            // Updated last interaction date
                                        }

                                        NewRow["conversationminmos"] = decimal.Round(ConvData.mediaStatsMinConversationMos, 2);
                                        NewRow["originaldirection"] = ConvData.originatingDirection;
                                        NewRow["participantid"] = ConvPart.participantId;
                                        if (ConvPart.participantName != null && ConvPart.participantName.Length > 250)
                                            NewRow["participantname"] = ConvPart.participantName.Substring(0, 250);
                                        else
                                            NewRow["participantname"] = ConvPart.participantName;
                                        // Handle null purpose value to prevent NoNullAllowedException
                                        NewRow["purpose"] = !string.IsNullOrEmpty(ConvPart.purpose) ? ConvPart.purpose : "unknown";

                                        NewRow["mediatype"] = ConvSess.mediaType;
                                        DRPartSumm["mediaType"] = ConvSess.mediaType;

                                        if (ConvSess.ani != null && ConvSess.ani.Length > 300)
                                            NewRow["ani"] = ConvSess.ani.Substring(0, 300);
                                        else
                                            NewRow["ani"] = ConvSess.ani;

                                        if (ConvSeg.queueId != null)
                                        {
                                            NewRow["queueid"] = ConvSeg.queueId;
                                            DRPartSumm["queueid"] = ConvSeg.queueId;
                                        }
                                        if (ConvPart.userId != null)
                                        {
                                            NewRow["userid"] = ConvPart.userId;
                                            DRPartSumm["userid"] = ConvPart.userId;
                                        }

                                        if (ConvSess.dnis != null && ConvSess.dnis.Length > 300)
                                            NewRow["dnis"] = ConvSess.dnis.Substring(0, 300);
                                        else
                                            NewRow["dnis"] = ConvSess.dnis;

                                        if (ConvSess.sessionDnis != null && ConvSess.sessionDnis.Length > 300)
                                            NewRow["sessiondnis"] = ConvSess.sessionDnis.Substring(0, 300);
                                        else
                                            NewRow["sessiondnis"] = ConvSess.sessionDnis;

                                        NewRow["sessiondirection"] = ConvSess.direction;
                                        NewRow["edgeId"] = ConvSess.edgeId;
                                        if (ConvSess.remote != null && ConvSess.remote.Length > 250)
                                            NewRow["remotedisplayable"] = ConvSess.remote.Substring(0, 249);
                                        else
                                            NewRow["remotedisplayable"] = ConvSess.remote;

                                        NewRow["conversationminrfactor"] = decimal.Round(ConvData.mediaStatsMinConversationRFactor, 2);

                                        if (ConvData.externalTag != null)
                                            NewRow["externalTag"] = ConvData.externalTag;

                                        // ConvSeg.segmentStart is already UTC from API deserialization
                                        var segmentStartUtc = ConvSeg.segmentStart;
                                        NewRow["segmentstartdate"] = segmentStartUtc;
                                        NewRow["segmentstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(segmentStartUtc, AppTimeZone);

                                        System.TimeSpan Diff = new System.TimeSpan();

                                        if (ConvSeg.segmentEnd > DateTime.UtcNow.AddYears(-20))
                                        {
                                            // ConvSeg.segmentEnd is already UTC from API deserialization
                                            var segmentEndUtc = ConvSeg.segmentEnd;
                                            NewRow["segmentenddate"] = segmentEndUtc;
                                            NewRow["segmentenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(segmentEndUtc, AppTimeZone);
                                            Diff = ConvSeg.segmentEnd - ConvSeg.segmentStart;
                                            NewRow["segmenttime"] = Diff.TotalSeconds;
                                            Diff = ConvSeg.segmentEnd - ConvData.conversationStart;
                                            NewRow["convtosegmentendtime"] = Diff.TotalSeconds;
                                        }
                                        else
                                        {
                                            NewRow["segmentenddate"] = System.DBNull.Value;
                                            NewRow["segmenttime"] = System.DBNull.Value;
                                            NewRow["convtosegmentendtime"] = System.DBNull.Value;
                                        }

                                        Diff = ConvSeg.segmentStart - ConvData.conversationStart;
                                        NewRow["convtosegmentstarttime"] = Diff.TotalSeconds;

                                        NewRow["segmenttype"] = ConvSeg.segmentType;
                                        NewRow["conference"] = ConvertBoolean(ConvSeg.conference);
                                        NewRow["segdestinationConversationId"] = ConvSeg.destinationConversationId;

                                        string RowWrapUp = ConvSeg.wrapUpCode;
                                        string RowWrapUpNote = ConvSeg.wrapUpNote;
                                        if (RowWrapUp != null)
                                        {
                                            if (RowWrapUp == "ININ-WRAP-UP-TIMEOUT")
                                                RowWrapUp = "00000000-0000-0000-0000-0000000000000";
                                            NewRow["wrapupcode"] = RowWrapUp;
                                            DRPartSumm["wrapupcode"] = RowWrapUp;
                                            if (RowWrapUpNote != null)
                                            {
                                                NewRow["wrapupnote"] = RowWrapUpNote;
                                                DRPartSumm["wrapupnote"] = RowWrapUpNote;
                                            }
                                            else
                                            {
                                                NewRow["wrapupnote"] = "";
                                            }
                                        }
                                        else
                                        {
                                            NewRow["wrapupcode"] = "";
                                            NewRow["wrapupnote"] = "";
                                        }

                                        if (ConvSeg.disconnectType == null)
                                            NewRow["disconnectiontype"] = "none";
                                        else
                                            NewRow["disconnectiontype"] = ConvSeg.disconnectType;

                                        NewRow["recordingexists"] = ConvertBoolean(ConvSess.recording);
                                        NewRow["sessionprovider"] = ConvSess.provider;

                                        // Map new Session-level fields
                                        NewRow["sessionid"] = ConvSess.sessionId;
                                        NewRow["protocolcallid"] = ConvSess.protocolCallId;
                                        NewRow["remotenamedisplayable"] = ConvSess.remoteNameDisplayable;
                                        NewRow["callbackusername"] = ConvSess.callbackUserName;

                                        // Handle callback numbers array - convert to JSON string
                                        if (ConvSess.callbackNumbers != null && ConvSess.callbackNumbers.Length > 0)
                                        {
                                            NewRow["callbacknumbers"] = System.Text.Json.JsonSerializer.Serialize(ConvSess.callbackNumbers);
                                        }
                                        else
                                        {
                                            NewRow["callbacknumbers"] = DBNull.Value;
                                        }

                                        NewRow["scriptid"] = ConvSess.scriptId;
                                        NewRow["skipenabled"] = ConvertBoolean(ConvSess.skipEnabled);
                                        NewRow["timeoutseconds"] = ConvSess.timeoutSeconds;
                                        NewRow["flowouttype"] = ConvSess.flowOutType;
                                        NewRow["roomid"] = ConvSess.roomId;

                                        // Handle callback scheduled time
                                        if (ConvSess.callbackScheduledTime > DateTime.UtcNow.AddYears(-20))
                                        {
                                            NewRow["callbackscheduledtime"] = ConvSess.callbackScheduledTime;
                                        }
                                        else
                                        {
                                            NewRow["callbackscheduledtime"] = DBNull.Value;
                                        }

                                        // Map new Participant-level fields
                                        NewRow["externalcontactid"] = ConvPart.externalContactId;
                                        NewRow["externalorganizationid"] = ConvPart.externalOrganizationId;
                                        DRPartSumm["externalcontactid"] = ConvPart.externalContactId;
                                        DRPartSumm["externalorganizationid"] = ConvPart.externalOrganizationId;

                                        // Map new Segment-level fields
                                        // Handle requested routing skill IDs array - convert to JSON string
                                        if (ConvSeg.requestedRoutingSkillIds != null && ConvSeg.requestedRoutingSkillIds.Length > 0)
                                        {
                                            NewRow["requestedroutingskillids"] = System.Text.Json.JsonSerializer.Serialize(ConvSeg.requestedRoutingSkillIds);
                                        }
                                        else
                                        {
                                            NewRow["requestedroutingskillids"] = DBNull.Value;
                                        }

                                        // Handle SIP response codes array - convert to JSON string
                                        if (ConvSeg.sipResponseCodes != null && ConvSeg.sipResponseCodes.Length > 0)
                                        {
                                            NewRow["sipresponsecodes"] = System.Text.Json.JsonSerializer.Serialize(ConvSeg.sipResponseCodes);
                                        }
                                        else
                                        {
                                            NewRow["sipresponsecodes"] = DBNull.Value;
                                        }

                                        // Handle Q850 response codes array - convert to JSON string
                                        if (ConvSeg.q850ResponseCodes != null && ConvSeg.q850ResponseCodes.Length > 0)
                                        {
                                            NewRow["q850responsecodes"] = System.Text.Json.JsonSerializer.Serialize(ConvSeg.q850ResponseCodes);
                                        }
                                        else
                                        {
                                            NewRow["q850responsecodes"] = DBNull.Value;
                                        }

                                        NewRow["errorcode"] = ConvSeg.errorCode;
                                        NewRow["requestedlanguageid"] = ConvSeg.requestedLanguageId;

                                        // Map Media Endpoint Statistics fields
                                        if (ConvSess.mediaEndpointStats != null && ConvSess.mediaEndpointStats.Length > 0)
                                        {
                                            var mediaStats = ConvSess.mediaEndpointStats[0]; // Use first endpoint stats

                                            // Handle codecs array - convert to JSON string
                                            if (mediaStats.codecs != null && mediaStats.codecs.Length > 0)
                                            {
                                                NewRow["codecs"] = System.Text.Json.JsonSerializer.Serialize(mediaStats.codecs);
                                            }
                                            else
                                            {
                                                NewRow["codecs"] = DBNull.Value;
                                            }

                                            NewRow["minmos"] = mediaStats.minMos;
                                            NewRow["minrfactor"] = mediaStats.minRFactor;
                                            NewRow["maxlatencyms"] = mediaStats.maxLatencyMs;
                                            NewRow["receivedpackets"] = mediaStats.receivedPackets;
                                            NewRow["discardedpackets"] = mediaStats.discardedPackets;
                                            NewRow["overrunpackets"] = mediaStats.overrunPackets;
                                            NewRow["invalidpackets"] = mediaStats.invalidPackets;
                                            NewRow["duplicatepackets"] = mediaStats.duplicatePackets;
                                        }
                                        else
                                        {
                                            // Set all media stats fields to null if no stats available
                                            NewRow["codecs"] = DBNull.Value;
                                            NewRow["minmos"] = DBNull.Value;
                                            NewRow["minrfactor"] = DBNull.Value;
                                            NewRow["maxlatencyms"] = DBNull.Value;
                                            NewRow["receivedpackets"] = DBNull.Value;
                                            NewRow["discardedpackets"] = DBNull.Value;
                                            NewRow["overrunpackets"] = DBNull.Value;
                                            NewRow["invalidpackets"] = DBNull.Value;
                                            NewRow["duplicatepackets"] = DBNull.Value;
                                        }

                                        if (CurrentSegment == SegmentCount && ConvSessFlow != null && ConvSessFlow.flowId != null)
                                        {
                                            NewRow["flowid"] = ConvSessFlow.flowId;
                                            NewRow["flowname"] = ConvSessFlow.flowName;
                                            try
                                            {
                                                NewRow["flowversion"] = decimal.Round(decimal.Parse(ConvSessFlow.flowVersion), 2);
                                            }
                                            catch
                                            {
                                                NewRow["flowversion"] = 1.0;
                                            }

                                            NewRow["flowtype"] = ConvSessFlow.flowType;
                                            NewRow["exitreason"] = ConvSessFlow.exitReason;
                                            NewRow["entryreason"] = ConvSessFlow.entryReason;
                                            NewRow["entrytype"] = ConvSessFlow.entryType;
                                            NewRow["transfertype"] = ConvSessFlow.transferType;
                                            NewRow["transfertargetname"] = ConvSessFlow.transferTargetName;
                                            NewRow["issuedcallback"] = ConvertBoolean(ConvSessFlow.issuedCallback);

                                            // Map new Flow-level fields
                                            NewRow["transfertargetaddress"] = ConvSessFlow.transferTargetAddress;
                                            NewRow["startinglanguage"] = ConvSessFlow.startingLanguage;
                                            NewRow["endinglanguage"] = ConvSessFlow.endingLanguage;

                                            // Use the centralized FlowOutcomeProcessor for consistent processing
                                            if (ConvSessFlow.outcomes != null && ConvSessFlow.outcomes.Any())
                                            {
                                                try
                                                {
                                                    var flowProcessor = new FlowOutcomeProcessor(_logger, AppTimeZone);
                                                    var flowOutcomeResult = await flowProcessor.ProcessFlowOutcomesAsync(
                                                        new[] { ConvSessFlow },
                                                        ConvData.conversationId,
                                                        ConvData.conversationStart,
                                                        ConvData.conversationEnd,
                                                        FlowOutcomes,
                                                        throwOnInvalidDates: true);

                                                    // Add "F" to status string to indicate flow outcome processing
                                                    if (flowOutcomeResult.TotalProcessed > 0)
                                                    {
                                                        // Flow outcomes processed
                                                    }
                                                }
                                                catch (Exception ex)
                                                {
                                                    _logger?.LogError(ex, "Error processing flow outcomes for conversation {ConversationId}: {ErrorMessage}",
                                                        ConvData.conversationId, ex.Message);
                                                    // Continue processing other conversations
                                                }
                                            }

                                        }

                                        if (CurrentSegment == SegmentCount && ConvSess.metrics != null && ConvSess.metrics.Length > 0)
                                        {
                                            foreach (Interactions.Metric ConvSessMetric in ConvSess.metrics)
                                            {
                                                string FirstChar = ConvSessMetric.name.Substring(0, 1);
                                                try
                                                {
                                                    switch (FirstChar)
                                                    {
                                                        case "n":
                                                            if (ConvSessMetric.value > 0)
                                                            {
                                                                NewRow[ConvSessMetric.name] = ConvSessMetric.value;
                                                                DRPartSumm[ConvSessMetric.name] = ConvSessMetric.value;
                                                            }
                                                            break;
                                                        case "t":
                                                            if (ConvSessMetric.value > 0)
                                                            {
                                                                if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                                                    ConvSessMetric.value += 100;

                                                                if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                                                {
                                                                    NewRow[ConvSessMetric.name] = Math.Round(ConvSessMetric.value / 1000.00F, 2) + 0.11;
                                                                    DRPartSumm[ConvSessMetric.name] = Math.Round(ConvSessMetric.value / 1000.00F, 2) + 0.11;
                                                                }
                                                                else
                                                                {
                                                                    NewRow[ConvSessMetric.name] = Math.Round(ConvSessMetric.value / 1000.00F, 2);
                                                                    DRPartSumm[ConvSessMetric.name] = Math.Round(ConvSessMetric.value / 1000.00F, 2);
                                                                }
                                                            }
                                                            break;
                                                    }
                                                }
                                                catch (Exception e)
                                                {
                                                    _logger?.LogError(e, "No Row For {MetricName} in conversation {ConversationId}", ConvSessMetric.name, ConvData?.conversationId ?? "Unknown");
                                                }
                                            }
                                        }

                                        try
                                        {
                                            DataRow CheckRowExists = DetailInteraction.Select("keyid= '" + NewRow["keyid"] + "'").FirstOrDefault();
                                            if (CheckRowExists == null)
                                            {
                                                DetailInteraction.Rows.Add(NewRow);
                                                // Record added - using debug level to avoid log flooding
                                            }
                                            else
                                            {
                                                // Duplicate record found - using debug level to avoid log flooding
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger?.LogError(ex, "Exception caught in Interaction Detail Module for conversation {ConversationId}", ConvData?.conversationId ?? "Unknown");
                                            throw;
                                        }

                                        CurrentSegment++;
                                        // Segment processed - using debug level to avoid log flooding
                                    }
                                }

                                if (CheckPartExists == null)
                                {
                                    ParticipantSummary.Rows.Add(DRPartSumm);
                                    // Participant added - using debug level to avoid log flooding
                                }
                                else
                                {
                                    // Participant duplicate found - using debug level to avoid log flooding
                                }
                            }
                        }
                    }

                    // Log summary for this page
                    _logger?.LogInformation("Completed processing outstanding conversations page {Page}: {ProcessedCount}/{TotalCount} conversations processed successfully",
                        currentPage, conversationCount, totalConversationsInBatch);
                }
                else
                {
                    // This should not happen due to earlier checks, but log it just in case
                    _logger?.LogWarning("Skipping page {Page} due to insufficient JSON data (length: {JsonLength})", currentPage, JsonString?.Length ?? 0);
                }

                currentPage++;
            }

            _logger?.LogInformation("Returning {RowCount} detailed interaction rows", DetailInteraction.Rows.Count);

            // Log final summary of outstanding conversations processing
            _logger?.LogInformation("Outstanding conversations processing completed. Total pages processed: {TotalPages}, DetailInteraction rows: {DetailRows}, ParticipantSummary rows: {ParticipantRows}, FlowOutcomes rows: {FlowRows}",
                currentPage - 1, DetailInteraction.Rows.Count, ParticipantSummary.Rows.Count, FlowOutcomes.Rows.Count);

            DataSet ReturnInteractionData = new DataSet();
            ReturnInteractionData.Tables.Add(DetailInteraction);
            ReturnInteractionData.Tables.Add(ParticipantSummary);
            ReturnInteractionData.Tables.Add(FlowOutcomes);
            return ReturnInteractionData;
        }

        public DataTable GetInteractionSummary(DataTable InteractionData)
        {
            return GetInteractionSummaryAsync(InteractionData).GetAwaiter().GetResult();
        }

        public async Task<DataTable> GetInteractionSummaryAsync(DataTable InteractionData)
        {
            _logger?.LogInformation("Producing Conversation Summary Data");

            // Create a stopwatch to measure total processing time
            var totalStopwatch = Stopwatch.StartNew();
            var segmentStopwatch = Stopwatch.StartNew();

            // Create a table to hold unique conversation IDs
            DataTable dt = new DataTable();
            dt.Clear();
            dt.TableName = "InteractionIds";
            dt.Columns.Add("conversationId");

            // Get unique conversation IDs using HashSet for better performance and thread safety
            HashSet<string> uniqueConversationIds = new HashSet<string>();

            foreach (DataRow InteractionRow in InteractionData.Rows)
            {
                // Capture conversation ID once to avoid threading issues
                string conversationId = InteractionRow["conversationId"]?.ToString();

                // Skip if conversation ID is null or already processed
                if (string.IsNullOrEmpty(conversationId) || !uniqueConversationIds.Add(conversationId))
                {
                    continue;
                }

                DataRow InteractionIdRow = dt.NewRow();
                InteractionIdRow["conversationId"] = conversationId;
                dt.Rows.Add(InteractionIdRow);
            }

            _logger?.LogInformation("Found {Count} unique conversations to process", dt.Rows.Count);

            // Since all data is already in memory, we can process in parallel
            // but need to be careful with thread safety
            var syncLock = new object();
            int CounterCSRows = 0;

            // Create batches of conversations to process in parallel
            // Use a larger batch size to reduce the number of tasks
            int batchSize = 500;
            var batches = new List<List<DataRow>>();

            for (int i = 0; i < dt.Rows.Count; i += batchSize)
            {
                var batch = new List<DataRow>();
                for (int j = i; j < Math.Min(i + batchSize, dt.Rows.Count); j++)
                {
                    batch.Add(dt.Rows[j]);
                }
                batches.Add(batch);
            }

            // Limit the number of concurrent tasks to avoid thread contention
            // This is similar to how it's done in the interaction processing
            int maxConcurrentTasks = Environment.ProcessorCount; // Use at most 4 threads or number of CPU cores, whichever is less
            _logger?.LogInformation("Processing with maximum {MaxThreads} concurrent threads", maxConcurrentTasks);

            // Process batches with limited concurrency
            var semaphore = new SemaphoreSlim(maxConcurrentTasks);
            var tasks = new List<Task>();

            foreach (var batch in batches)
            {
                await semaphore.WaitAsync();

                tasks.Add(Task.Run(async () => {
                    try
                    {
                        // Process each conversation in this batch
                        foreach (DataRow InteractionRow in batch)
                        {
                            // Capture conversation ID once to avoid threading issues
                            string conversationId = InteractionRow["conversationId"]?.ToString();
                            if (string.IsNullOrEmpty(conversationId))
                            {
                                continue;
                            }

                            // Create a filtered table with just this conversation's data using LINQ for safety
                            DataTable InteractDetails = InteractionData.Clone();
                            var filteredRows = InteractionData.AsEnumerable()
                                .Where(row => row.Field<string>("conversationid") == conversationId);

                            foreach (DataRow DRInteraction in filteredRows)
                            {
                                InteractDetails.ImportRow(DRInteraction);
                            }

                            // Process this conversation
                            lock (syncLock) // Lock when calling ConvSummary to avoid thread contention on ConversationSummaryData
                            {
                                ConvSummary(InteractDetails);
                            }

                            // Thread-safe increment of counter
                            int localCount;
                            lock (syncLock)
                            {
                                CounterCSRows++;
                                localCount = CounterCSRows;
                            }

                            // Log progress every 100 rows
                            if (localCount % 100 == 0)
                            {
                                _logger?.LogDebug("Conversation summary processing: {RowCount} of {TotalRows} rows processed",
                                    localCount, dt.Rows.Count);
                            }
                        }
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                }));
            }

            // Wait for all tasks to complete
            await Task.WhenAll(tasks);

            _logger?.LogInformation("Processed all {Count} conversation summaries in {ElapsedSeconds:F2} seconds",
                CounterCSRows, totalStopwatch.Elapsed.TotalSeconds);

            // Summary logging for conversation summary constraint violations (replaces repetitive individual debug messages)
            if (_conversationSummaryConstraintViolations > 0)
            {
                _logger?.LogInformation("Conversation summary constraint violation summary: {ConstraintViolations} duplicate conversation summary constraint violations suppressed to reduce log noise", _conversationSummaryConstraintViolations);
            }

            return ConversationSummaryData;
        }

        private Boolean ConvSummary(DataTable Conversation)
        {
            DataView view = new DataView(Conversation);
            view.Sort = "segmentstartdate asc";

            if (Conversation.Rows.Count > 0)
            {
                DataRow ConvSummaryRow = ConversationSummaryData.NewRow();

                decimal TalkTime = 0;
                decimal WrapUpTime = 0;
                decimal QueueTime = 0;
                DateTime LastSegmentEnd = (DateTime)Conversation.Rows[0]["conversationstartdate"];
                string ANI = Conversation.Rows[0]["ani"].ToString();
                string DNIS = Conversation.Rows[0]["dnis"].ToString();

                string FirstAgentId = string.Empty;
                string LastAgentId = string.Empty;

                string FirstQueueId = string.Empty;
                string LastQueueId = string.Empty;

                string LastPurpose = string.Empty;
                string LastDisconnect = string.Empty;

                string FirstWrapUpCode = string.Empty;
                string LastWrapUpCode = string.Empty;

                string LastPeer = string.Empty;

                bool FirstAgent = false;
                bool FirstWrapUp = false;
                bool FirstQueue = false;

                int TotalHold = 0;
                decimal TotalHoldTime = 0;

                string LastMediaType = Conversation.Rows[0]["mediatype"].ToString();
                int TotalColdTrans = 0;
                int TotalWarmTrans = 0;
                int HandleCount = 0;
                decimal TotalHandleTime = 0;
                int AnswerCount = 0;
                decimal TotalAnswerTime = 0;

                int AbandonCount = 0;

                int ResponseCount = 0;
                decimal TotalResponseTime = 0;

                bool CallBackDetected = false;
                int CallBackPart = 0;

                decimal LastSegmentTime = 0;

                foreach (DataRow Segment in Conversation.Rows)
                {
                    if (Segment["purpose"].ToString() == "agent" || Segment["purpose"].ToString() == "acd")
                    {
                        string[] GenCodeSplit = Segment["gencode"].ToString().Split('|');

                        if (Segment["mediatype"].ToString() == "callback")
                        {
                            CallBackDetected = true;
                            CallBackPart = int.Parse(GenCodeSplit[0]);
                        }

                        if (Segment["segmenttype"].ToString() == "wrapup")
                        {
                            if (!FirstWrapUp)
                            {
                                FirstWrapUp = true;
                                FirstWrapUpCode = Segment["wrapupcode"].ToString();
                            }

                            LastWrapUpCode = Segment["wrapupcode"].ToString();
                        }

                        if (Segment["tabandon"] != null && Segment["tabandon"] != System.DBNull.Value && float.TryParse(Segment["tabandon"].ToString(), out float tabandonValue) && tabandonValue > 0)
                            AbandonCount = AbandonCount + 1;

                        if (Segment["peer"] != null && Segment["peer"].ToString() != "")
                            LastPeer = Segment["peer"].ToString();

                        if (CallBackDetected == true && int.Parse(GenCodeSplit[0]) == CallBackPart && Segment["mediatype"].ToString() != "callback")
                        {
                        }
                        else
                        {
                            if (Segment["segmenttype"].ToString() == "interact")
                            {
                                LastMediaType = Segment["mediatype"].ToString();

                                if (Segment["segmenttime"] != null && Segment["segmenttime"] != System.DBNull.Value && Segment["segmenttime"].ToString() != "")
                                {
                                    if (Segment["purpose"].ToString() == "agent")
                                    {
                                        TalkTime += Convert.ToDecimal(Segment["segmenttime"]);
                                        LastAgentId = Segment["userid"].ToString();
                                        LastQueueId = Segment["queueid"].ToString();

                                        if (!FirstAgent)
                                        {
                                            FirstAgentId = Segment["userid"].ToString();
                                            FirstAgent = true;
                                        }
                                    }
                                    else
                                    {
                                        QueueTime += Convert.ToDecimal(Segment["segmenttime"]);
                                    }

                                    LastSegmentEnd = (DateTime)Segment["segmentenddate"];
                                }

                                if (!FirstQueue)
                                {
                                    FirstQueueId = Segment["queueid"].ToString();
                                    LastQueueId = Segment["queueid"].ToString();
                                    FirstQueue = true;
                                }
                                else
                                {
                                    LastQueueId = Segment["queueid"].ToString();
                                }
                            }

                            if (Segment["segmenttype"].ToString() == "wrapup")
                            {
                                if (Segment["segmenttime"] != null  && Segment["segmenttime"] != System.DBNull.Value)
                                {
                                    if (Convert.ToInt32(Segment["segmenttime"]) == 0)
                                    {
                                        decimal TempWrapUpTime = Convert.ToDecimal((LastSegmentEnd - (DateTime)Segment["segmentstartdate"]).TotalSeconds);
                                        WrapUpTime += TempWrapUpTime;
                                        TalkTime = TalkTime - TempWrapUpTime;
                                    }
                                    else
                                    {
                                        WrapUpTime += Convert.ToDecimal(Segment["segmenttime"]);
                                    }
                                }
                            }

                            if (Segment["ani"] != null && Segment["ani"] != System.DBNull.Value)
                            {
                                if (Conversation.Rows[0]["mediatype"].ToString() == "email" || Conversation.Rows[0]["mediatype"].ToString() == "chat")
                                    ANI = Segment["remotedisplayable"].ToString();
                            }

                            if (Segment["theldcomplete"] != null && Segment["theldcomplete"] != System.DBNull.Value && Segment["theldcomplete"].ToString() != "")
                                TotalHoldTime += Convert.ToDecimal(Segment["theldcomplete"]);

                            if (Segment["nconsulttransferred"] != null && Segment["nconsulttransferred"] != System.DBNull.Value && Segment["nconsulttransferred"].ToString() != "")
                                TotalWarmTrans += int.Parse(Segment["nconsulttransferred"].ToString());

                            if (Segment["nblindtransferred"] != null && Segment["nblindtransferred"] != System.DBNull.Value && Segment["nblindtransferred"].ToString() != "")
                                TotalColdTrans += int.Parse(Segment["nblindtransferred"].ToString());

                            if (Segment["thandle"] != null && Segment["thandle"] != System.DBNull.Value && Segment["thandle"].ToString() != "")
                            {
                                HandleCount += 1;
                                TotalHandleTime += Convert.ToDecimal(Segment["thandle"]);
                            }

                            if (Segment["tanswered"] != null && Segment["tanswered"] != System.DBNull.Value && Segment["tanswered"].ToString() != "")
                            {
                                AnswerCount += 1;
                                TotalAnswerTime += Convert.ToDecimal(Segment["tanswered"]);
                            }

                            if (Segment["tagentresponsetime"] != null && Segment["tagentresponsetime"] != System.DBNull.Value && Segment["tagentresponsetime"].ToString() != "")
                            {
                                ResponseCount += 1;
                                TotalResponseTime += Convert.ToDecimal(Segment["tagentresponsetime"]);
                            }

                            if (Segment["segmenttype"].ToString() == "hold")
                                TotalHold = TotalHold + 1;
                        }

                        if (CallBackPart > 0 && int.Parse(GenCodeSplit[0]) > CallBackPart)
                        {
                            CallBackPart = 0;
                            CallBackDetected = false;
                        }

                        LastDisconnect = Segment["disconnectiontype"].ToString();
                        LastPurpose = Segment["purpose"].ToString();

                        if (Segment["segmenttime"] != null && Segment["segmenttime"] != System.DBNull.Value && Segment["segmenttime"].ToString() != "")
                        {
                            LastSegmentTime = Convert.ToDecimal(Segment["segmenttime"]);
                        }
                    }
                }

                ConvSummaryRow["conversationid"] = Conversation.Rows[0]["conversationid"];
                ConvSummaryRow["keyid"] = Conversation.Rows[0]["conversationid"];
                ConvSummaryRow["conversationstartdate"] = Conversation.Rows[0]["conversationstartdate"];
                ConvSummaryRow["conversationenddate"] = Conversation.Rows[0]["conversationenddate"];
                ConvSummaryRow["conversationstartdateltc"] = Conversation.Rows[0]["conversationstartdateltc"];
                ConvSummaryRow["conversationenddateltc"] = Conversation.Rows[0]["conversationenddateltc"];
                ConvSummaryRow["originaldirection"] = Conversation.Rows[0]["originaldirection"];
                ConvSummaryRow["firstmediatype"] = Conversation.Rows[0]["mediatype"];
                ConvSummaryRow["lastmediatype"] = LastMediaType;
                ConvSummaryRow["ani"] = ANI;
                ConvSummaryRow["dnis"] = DNIS;
                ConvSummaryRow["peer"] = LastPeer;
                ConvSummaryRow["firstagentid"] = FirstAgentId;
                ConvSummaryRow["lastagentid"] = LastAgentId;
                ConvSummaryRow["firstqueueid"] = FirstQueueId;
                ConvSummaryRow["lastqueueid"] = LastQueueId;
                ConvSummaryRow["ttalkcomplete"] = TalkTime;
                ConvSummaryRow["tqueuetime"] = QueueTime;
                ConvSummaryRow["tacw"] = WrapUpTime;
                ConvSummaryRow["tabandonedcount"] = AbandonCount;
                ConvSummaryRow["firstwrapupcode"] = FirstWrapUpCode;
                ConvSummaryRow["lastwrapupcode"] = LastWrapUpCode;
                ConvSummaryRow["theldcompletecount"] = TotalHold;
                ConvSummaryRow["theldcomplete"] = TotalHoldTime;
                ConvSummaryRow["thandlecount"] = HandleCount;
                ConvSummaryRow["thandle"] = TotalHandleTime;
                ConvSummaryRow["tansweredcount"] = AnswerCount;
                ConvSummaryRow["tanswered"] = TotalAnswerTime;
                ConvSummaryRow["tresponsecount"] = ResponseCount;
                ConvSummaryRow["tresponse"] = TotalResponseTime;
                ConvSummaryRow["nconsulttransferred"] = TotalWarmTrans;
                ConvSummaryRow["nblindtransferred"] = TotalColdTrans;
                ConvSummaryRow["lastdisconnect"] = LastDisconnect;
                ConvSummaryRow["lastpurpose"] = LastPurpose;
                ConvSummaryRow["lastsegmenttime"] = LastSegmentTime;
                ConvSummaryRow["divisionid"] = Conversation.Rows[0]["divisionid"];
                ConvSummaryRow["divisionid2"] = Conversation.Rows[0]["divisionid2"];
                ConvSummaryRow["divisionid3"] = Conversation.Rows[0]["divisionid3"];

                try
                {
                    ConversationSummaryData.Rows.Add(ConvSummaryRow);
                }
                catch (Exception ex)
                {
                    // Suppress individual constraint violation debug messages to reduce log noise
                    Interlocked.Increment(ref _conversationSummaryConstraintViolations);
                }
            }

            return true;
        }

        public DataSet GetDetailInteractionDataFromGCQuery(String StartDate, String EndDate)
        {
            return GetDetailInteractionDataFromGCQueryAsync(StartDate, EndDate).GetAwaiter().GetResult();
        }

        public async Task<DataSet> GetDetailInteractionDataFromGCQueryAsync(String StartDate, String EndDate)
        {
            // Clear processed keys for fresh processing run
            _processedParticipantKeys.Clear();
            _processedDetailInteractionKeys.Clear();

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            _logger?.LogInformation("Retrieving detail interaction data from date: {StartDate} to {EndDate}", StartDate, EndDate);
            int PageNumber = 1;
            bool FoundData = true;

            _logger?.LogDebug("Creating memory tables for query mode processing");
            DataTable DetailInteraction = DBUtil.CreateInMemTable("detailedInteractionData");
            DataTable ParticipantSummary = DBUtil.CreateInMemTable("participantsummaryData");

            DataTable FlowOutcomes = DBUtil.CreateInMemTable("flowoutcomedata");

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();

            // PERFORMANCE ENHANCEMENT: Process pages concurrently instead of sequentially
            // First, determine total number of pages by making an initial request
            _logger?.LogInformation("ConversationDetails: Determining total pages for concurrent processing");

            string initialRequestBody = "{" +
                            " \"interval\": \"" + StartDate + "/" + EndDate + "\", " +
                            " \"order\": \"asc\"," +
                            " \"orderBy\": \"conversationEnd\"," +
                            " \"flattenMultivaluedDimensions\": true," +
                            " \"dimensions\": [\"conversationId\", \"participantId\", \"sessionId\", \"flowId\", \"flowName\", \"flowType\", \"flowVersion\", \"flowOutcomeId\", \"flowOutcomeName\", \"flowOutcomeValue\"]," +
                            " \"paging\": {" +
                            " \"pageSize\": 100," +
                            "  \"pageNumber\": 1" +
                            "}}";

            string initialJsonString = JsonActions.JsonReturnString(URI + "/api/v2/analytics/conversations/details/query", GCApiKey, initialRequestBody);

            if (string.IsNullOrEmpty(initialJsonString) || initialJsonString.Length < 50)
            {
                _logger?.LogInformation("ConversationDetails: No data found for the specified date range");
                FoundData = false;
            }
            else
            {
                // Parse initial response to determine total pages
                JObject initialResponse = JObject.Parse(initialJsonString);
                int totalHits = initialResponse["totalHits"]?.ToObject<int>() ?? 0;
                int totalPages = (int)Math.Ceiling(totalHits / 100.0);

                _logger?.LogInformation("ConversationDetails: Found {TotalHits} conversations across {TotalPages} pages - processing concurrently", totalHits, totalPages);

                if (totalPages > 0)
                {
                    // CONCURRENT PROCESSING: Process all pages concurrently for better performance
                    await ProcessConversationDetailsPagesAsync(URI, GCApiKey, StartDate, EndDate, totalPages, DetailInteraction, ParticipantSummary, FlowOutcomes, AppTimeZone);
                }

                FoundData = false; // Exit the while loop since we've processed all pages
            }

            // Sequential processing has been replaced with concurrent processing above
            // The old while loop and JSON processing logic has been moved to ProcessConversationDetailsPagesAsync
            // This section is now handled by the concurrent page processing

            _logger?.LogInformation("Returning {RowCount} detailed interaction rows from query mode", DetailInteraction.Rows.Count);

            // COMMENTED OUT: First participant attributes call during main QUERY processing
            // This was causing duplicate processing - the second call during outstanding conversations is the required one
            // _logger?.LogInformation("Retrieving participant attribute data for QUERY mode");
            // DataTable ParticipantAttributes = await GetParticipantAttributesAsync(DetailInteraction);
            // ParticipantAttributes.TableName = "participantAttributesDynamic";

            // Create empty participant attributes table for now - will be populated by outstanding conversations processing
            DataTable ParticipantAttributes = DBUtil.CreateInMemTable("participantAttributesDynamic");
            ParticipantAttributes.TableName = "participantAttributesDynamic";

            DataSet ReturnInteractionData = new DataSet();
            ReturnInteractionData.Tables.Add(DetailInteraction);
            ReturnInteractionData.Tables.Add(ParticipantAttributes);
            ReturnInteractionData.Tables.Add(ParticipantSummary);
            ReturnInteractionData.Tables.Add(FlowOutcomes);

            return ReturnInteractionData;
        }

        public DataTable GetParticipantAttributes(DataTable InteractionData)
        {
            return GetParticipantAttributesAsync(InteractionData).GetAwaiter().GetResult();
        }

        public async Task<DataTable> GetParticipantAttributesAsync(DataTable InteractionData)
        {
            // Add comprehensive null checking at the start
            if (InteractionData == null)
            {
                _logger?.LogError("InteractionData parameter is null in GetParticipantAttributes");
                throw new ArgumentNullException(nameof(InteractionData), "InteractionData cannot be null");
            }

            if (GCControlData?.Tables?["GCControlData"]?.Rows == null || GCControlData.Tables["GCControlData"].Rows.Count == 0)
            {
                _logger?.LogError("GCControlData is null or empty in GetParticipantAttributes");
                throw new InvalidOperationException("GCControlData is not properly initialized");
            }

            if (string.IsNullOrEmpty(TimeZoneConfig))
            {
                _logger?.LogError("TimeZoneConfig is null or empty in GetParticipantAttributes");
                throw new InvalidOperationException("TimeZoneConfig is not properly configured");
            }

            // CRITICAL FIX: Always process ALL conversations returned from Genesys Cloud
            // Data integrity requires that any data returned from GC must be written to database
            // The previous "smart incremental processing" was incorrectly filtering out valid data
            var conversationIds = InteractionData.AsEnumerable()
                .Where(row => row["conversationid"] != null && row["conversationid"] != DBNull.Value)
                .Select(row => row["conversationid"].ToString())
                .Where(id => !string.IsNullOrEmpty(id))
                .ToList();

            _logger?.LogInformation("ParticipantAttributes: Processing ALL {TotalConversations} conversations returned from Genesys Cloud (data integrity fix)", conversationIds.Count);

            // REMOVED: Smart incremental processing logic that was incorrectly filtering out valid data
            // All conversations returned from GC should be processed unless filtered by diffing logic
            var conversationsToProcess = conversationIds.ToList();

            // Create the result table that will contain ALL participant attributes data
            DataTable ParticipantAttributes = DBUtil.CreateInMemTable("participantAttributesDynamic");

            // REMOVED: All smart incremental processing logic that was filtering out valid data
            // Data integrity fix: Process ALL conversations returned from Genesys Cloud

            _logger?.LogInformation("ParticipantAttributes: Processing {TotalCount} conversations returned from Genesys Cloud",
                conversationsToProcess.Count);

            int MaxNVarCharLength = 0;
            switch (DBUtil.DBType)
            {
                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                    MaxNVarCharLength = 200;
                    break;
                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                    MaxNVarCharLength = 50;
                    break;
                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                    MaxNVarCharLength = 100;
                    break;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }

            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            _logger?.LogInformation("Retrieving participant attribute data");

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"]?.ToString();
            if (string.IsNullOrEmpty(URI))
            {
                _logger?.LogError("GC_URL is null or empty in GetParticipantAttributes");
                throw new InvalidOperationException("GC_URL is not properly configured in GCControlData");
            }

            // If no new conversations to process, return the existing data
            if (conversationsToProcess.Count == 0)
            {
                return ParticipantAttributes;
            }

            // STEP 2: Process only NEW conversations that need participant attributes
            DataTable SmallConvTable = CreateSmallConversationDetails();

            // Create a separate table for newly processed participant attributes
            DataTable NewParticipantAttributes = DBUtil.CreateInMemTable("participantAttributesDynamic");

            // Use HashSet for efficient duplicate detection instead of string comparison
            HashSet<string> processedConversationIds = new HashSet<string>();

            // Filter conversations to only process those that need new processing
            List<DataRow> conversationsToProcessRows = InteractionData.AsEnumerable()
                .Where(row => row["conversationid"] != null && row["conversationid"] != DBNull.Value)
                .Where(row => conversationsToProcess.Contains(row["conversationid"].ToString()))
                .ToList();
            int duplicateConversationsFiltered = 0;

            _logger?.LogInformation("Processing {ConversationCount} NEW conversations for participant attributes (smart incremental processing)",
                conversationsToProcessRows.Count);

            foreach (DataRow Conversation in conversationsToProcessRows)
            {
                // Capture conversation ID once to avoid threading issues
                string currentConversationId = Conversation["conversationId"]?.ToString();

                // Skip if conversation ID is null or already processed
                if (string.IsNullOrEmpty(currentConversationId))
                {
                    continue;
                }

                // Track duplicate filtering for accurate reporting
                if (!processedConversationIds.Add(currentConversationId))
                {
                    duplicateConversationsFiltered++;
                    continue;
                }

                DataRow SmallConvRow = SmallConvTable.NewRow();
                SmallConvRow["conversationid"] = currentConversationId;
                SmallConvRow["mediatype"] = Conversation["mediatype"];
                SmallConvRow["conversationstartdate"] = Conversation["conversationstartdate"];
                SmallConvRow["conversationenddate"] = Conversation["conversationenddate"];
                SmallConvRow["conversationstartdateltc"] = Conversation["conversationstartdateltc"];
                SmallConvRow["conversationenddateltc"] = Conversation["conversationenddateltc"];
                SmallConvTable.Rows.Add(SmallConvRow);
            }

            int totalConversations = InteractionData.Rows.Count;
            int uniqueConversations = SmallConvTable.Rows.Count;

            // Enhanced logging to show duplicate filtering statistics
            _logger?.LogInformation("ParticipantAttributes:Filtering: Total conversations from InteractionData: {TotalConversations}", totalConversations);
            _logger?.LogInformation("ParticipantAttributes:Filtering: Unique conversations after deduplication: {UniqueConversations}", uniqueConversations);
            _logger?.LogInformation("ParticipantAttributes:Filtering: Duplicate conversations filtered out: {DuplicateCount}", duplicateConversationsFiltered);

            _logger?.LogInformation("Finished getting conversation IDs. Processing {UniqueConversations} unique conversations, filtered {DuplicateConversations} duplicates",
                uniqueConversations, duplicateConversationsFiltered);

            _logger?.LogInformation("ParticipantAttributes:Start: Processing {UniqueConversations} unique conversations (from {TotalConversations} total, {DuplicateCount} duplicates filtered)",
                uniqueConversations, totalConversations, duplicateConversationsFiltered);

            // Process NEW conversations in async batches for better performance
            await ProcessParticipantAttributesAsync(SmallConvTable, NewParticipantAttributes, URI, GCApiKey, MaxNVarCharLength);

            // STEP 3: Merge newly processed data with existing data
            _logger?.LogInformation("ParticipantAttributes: Merging {NewRows} newly processed rows with {ExistingRows} existing rows",
                NewParticipantAttributes.Rows.Count, ParticipantAttributes.Rows.Count);

            foreach (DataRow newRow in NewParticipantAttributes.Rows)
            {
                DataRow mergedRow = ParticipantAttributes.NewRow();
                foreach (DataColumn column in NewParticipantAttributes.Columns)
                {
                    if (ParticipantAttributes.Columns.Contains(column.ColumnName))
                    {
                        mergedRow[column.ColumnName] = newRow[column.ColumnName];
                    }
                }
                ParticipantAttributes.Rows.Add(mergedRow);
            }

            _logger?.LogInformation("ParticipantAttributes: Final result contains {TotalRows} participant attribute rows ({ExistingRows} existing + {NewRows} newly processed)",
                ParticipantAttributes.Rows.Count, ParticipantAttributes.Rows.Count - NewParticipantAttributes.Rows.Count, NewParticipantAttributes.Rows.Count);

            _logger?.LogInformation("ParticipantAttributes:Complete: Final result contains {TotalRows} rows covering {RequestedConversations} requested conversations ({NewlyProcessed} newly processed + {ExistingData} from existing data)",
                ParticipantAttributes.Rows.Count, conversationIds.Count, NewParticipantAttributes.Rows.Count, ParticipantAttributes.Rows.Count - NewParticipantAttributes.Rows.Count);

            // SMART VERIFICATION: Verify coverage for the UNIQUE conversations that were actually processed
            // The baseline should be unique conversations (SmallConvTable.Rows.Count), not total input conversations
            int expectedConversations = SmallConvTable.Rows.Count; // Unique conversations that should have been processed
            int actualRows = ParticipantAttributes.Rows.Count;

            _logger?.LogInformation("ParticipantAttributes:Verification: Comparing {ActualRows} result rows against {ExpectedConversations} unique conversations processed (from {TotalInput} total input conversations)",
                actualRows, expectedConversations, conversationIds.Count);

            // Analyze overall failure patterns
            int totalRateLimitFailures = 0;
            int totalEmptyResponseFailures = 0;
            int totalHttp202Failures = 0;
            int totalExceptionFailures = 0;
            int totalOtherFailures = 0;
            int conversationsWithAttributes = 0;
            int conversationsWithoutAttributes = 0;

            try
            {
                if (ParticipantAttributes.Columns.Contains("api_failure_reason"))
                {
                    var allFailureReasons = ParticipantAttributes.AsEnumerable()
                        .Where(row => row["api_failure_reason"] != null && row["api_failure_reason"] != System.DBNull.Value)
                        .Select(row => row["api_failure_reason"].ToString())
                        .Where(reason => !string.IsNullOrEmpty(reason))
                        .ToList();

                    totalRateLimitFailures = allFailureReasons.Count(r => r.Contains("Rate Limiting"));
                    totalEmptyResponseFailures = allFailureReasons.Count(r => r.Contains("Empty Response") || r.Contains("Short Response"));
                    totalHttp202Failures = allFailureReasons.Count(r => r.Contains("HTTP 202 Accepted"));
                    totalExceptionFailures = allFailureReasons.Count(r => r.Contains("Exception"));
                    totalOtherFailures = allFailureReasons.Count - totalRateLimitFailures - totalEmptyResponseFailures - totalHttp202Failures - totalExceptionFailures;

                    conversationsWithoutAttributes = allFailureReasons.Count;
                    conversationsWithAttributes = actualRows - conversationsWithoutAttributes;
                }
                else
                {
                    conversationsWithAttributes = actualRows;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Failed to analyze overall failure patterns");
                conversationsWithAttributes = actualRows;
            }

            if (actualRows < expectedConversations)
            {
                int missingCount = expectedConversations - actualRows;
                _logger?.LogError("CRITICAL: Missing participant attribute rows detected! Expected: {Expected}, Actual: {Actual}, Missing: {Missing}",
                    expectedConversations, actualRows, missingCount);

                // Enhanced debugging: Log detailed information about what's missing
                var processedIds = ParticipantAttributes.AsEnumerable()
                    .Select(row => row.Field<string>("conversationid"))
                    .Where(id => !string.IsNullOrEmpty(id))
                    .ToHashSet();

                _logger?.LogError("VERIFICATION DEBUG: Found {ProcessedIdsCount} processed conversation IDs in ParticipantAttributes table", processedIds.Count);

                // FIXED: Use SmallConvTable instead of allConversations for accurate missing conversation analysis
                // SmallConvTable contains the actual conversations that were sent for processing (after duplicate filtering)
                var allInputIds = SmallConvTable.AsEnumerable()
                    .Where(row => row["conversationid"] != null && row["conversationid"] != System.DBNull.Value)
                    .Select(row => row["conversationid"].ToString())
                    .Where(id => !string.IsNullOrEmpty(id))
                    .ToList();

                _logger?.LogError("VERIFICATION DEBUG: Found {InputIdsCount} input conversation IDs from SmallConvTable (unique conversations sent for processing)", allInputIds.Count);

                var missingIds = allInputIds
                    .Where(id => !processedIds.Contains(id))
                    .ToList();

                _logger?.LogError("VERIFICATION DEBUG: Identified {MissingIdsCount} missing conversation IDs", missingIds.Count);

                if (missingIds.Any())
                {
                    var sampleMissingIds = missingIds.Take(10).ToList();
                    _logger?.LogError("Sample missing conversation IDs: {MissingIds}", string.Join(", ", sampleMissingIds));

                    // Log additional details about the first few missing conversations
                    foreach (var missingId in sampleMissingIds.Take(3))
                    {
                        // FIXED: Use SmallConvTable for missing conversation details lookup
                        var missingConvRow = SmallConvTable.AsEnumerable().FirstOrDefault(row =>
                            row["conversationid"] != null &&
                            row["conversationid"] != System.DBNull.Value &&
                            row["conversationid"].ToString() == missingId);

                        if (missingConvRow != null)
                        {
                            var startDate = missingConvRow["conversationstartdate"]?.ToString() ?? "null";
                            var mediaType = missingConvRow["mediatype"]?.ToString() ?? "null";
                            _logger?.LogError("Missing conversation details - ID: {ConversationId}, StartDate: {StartDate}, MediaType: {MediaType}",
                                missingId, startDate, mediaType);
                        }
                    }

                    // RE-PROCESSING: Attempt to re-process just the missing conversations
                    await ReprocessMissingConversations(missingIds, SmallConvTable, ParticipantAttributes, URI, GCApiKey, MaxNVarCharLength);
                }

                // Check if there are any rows with null conversation IDs
                var nullIdRows = ParticipantAttributes.AsEnumerable()
                    .Where(row => string.IsNullOrEmpty(row.Field<string>("conversationid")))
                    .Count();

                if (nullIdRows > 0)
                {
                    _logger?.LogError("VERIFICATION DEBUG: Found {NullIdRows} rows in ParticipantAttributes with null/empty conversation IDs", nullIdRows);
                }
            }
            else
            {
                _logger?.LogInformation("ParticipantAttributes:Success: Complete coverage achieved - all {ConversationCount} conversations processed",
                    expectedConversations);

                // Additional verification: Check for data integrity
                var duplicateConversationIds = ParticipantAttributes.AsEnumerable()
                    .GroupBy(row => row.Field<string>("conversationid"))
                    .Where(g => g.Count() > 1)
                    .Select(g => g.Key)
                    .Where(id => !string.IsNullOrEmpty(id))
                    .ToList();

                if (duplicateConversationIds.Any())
                {
                    _logger?.LogWarning("ParticipantAttributes:Verification: Found {DuplicateCount} duplicate conversation IDs: {DuplicateIds}",
                        duplicateConversationIds.Count, string.Join(", ", duplicateConversationIds.Take(5)));
                }
                else
                {
                    _logger?.LogInformation("VERIFICATION SUCCESS: No duplicate conversation IDs found in ParticipantAttributes table");
                }
            }

            // Comprehensive final summary with rate limiting impact analysis
            if (totalRateLimitFailures > 0 || totalEmptyResponseFailures > 0 || totalHttp202Failures > 0 || totalExceptionFailures > 0)
            {
                _logger?.LogWarning("PARTICIPANT ATTRIBUTES PROCESSING SUMMARY:");
                _logger?.LogWarning("  Total Conversations from InteractionData: {TotalFromInteractionData}", totalConversations);
                _logger?.LogWarning("  Duplicate Conversations Filtered: {DuplicatesFiltered}", duplicateConversationsFiltered);
                _logger?.LogWarning("  Unique Conversations Processed: {UniqueConversations}", expectedConversations);
                _logger?.LogWarning("  Successfully Processed: {SuccessfulConversations} ({SuccessPercentage:P1})", conversationsWithAttributes, (double)conversationsWithAttributes / expectedConversations);
                _logger?.LogWarning("  Failed API Calls: {FailedConversations} ({FailurePercentage:P1})", conversationsWithoutAttributes, (double)conversationsWithoutAttributes / expectedConversations);

                if (totalRateLimitFailures > 0)
                {
                    _logger?.LogWarning("    - Rate Limiting Failures: {RateLimitFailures} ({RateLimitPercentage:P1}) - Consider reducing concurrent requests or implementing longer delays",
                        totalRateLimitFailures, (double)totalRateLimitFailures / expectedConversations);
                }
                if (totalEmptyResponseFailures > 0)
                {
                    _logger?.LogWarning("    - Empty/Short Response Failures: {EmptyResponseFailures} ({EmptyResponsePercentage:P1}) - May indicate server issues or network problems",
                        totalEmptyResponseFailures, (double)totalEmptyResponseFailures / expectedConversations);
                }
                if (totalHttp202Failures > 0)
                {
                    _logger?.LogWarning("    - HTTP 202 Accepted Responses: {Http202Failures} ({Http202Percentage:P1}) - Async operations in progress, data may be available later",
                        totalHttp202Failures, (double)totalHttp202Failures / expectedConversations);
                }
                if (totalExceptionFailures > 0)
                {
                    _logger?.LogWarning("    - Exception Failures: {ExceptionFailures} ({ExceptionPercentage:P1}) - Check network connectivity and server status",
                        totalExceptionFailures, (double)totalExceptionFailures / expectedConversations);
                }
                if (totalOtherFailures > 0)
                {
                    _logger?.LogWarning("    - Other Failures: {OtherFailures} ({OtherPercentage:P1})",
                        totalOtherFailures, (double)totalOtherFailures / expectedConversations);
                }

                _logger?.LogWarning("  All failed conversations have basic metadata rows to ensure no data loss.");

                if (totalHttp202Failures > 0)
                {
                    _logger?.LogInformation("HTTP 202 ACCEPTED RESPONSES EXPLANATION:");
                    _logger?.LogInformation("  HTTP 202 responses indicate that Genesys Cloud is processing the conversation data asynchronously.");
                    _logger?.LogInformation("  This is normal behavior for recent conversations or during high system load.");
                    _logger?.LogInformation("  The conversation data may become available in subsequent runs once processing completes.");
                    _logger?.LogInformation("  No action is required - these conversations have been recorded with basic metadata.");
                }

                if (totalRateLimitFailures > expectedConversations * 0.1) // More than 10% rate limit failures
                {
                    _logger?.LogError("HIGH RATE LIMITING DETECTED: {RateLimitPercentage:P1} of conversations failed due to rate limiting. Consider:");
                    _logger?.LogError("  1. Reducing the number of concurrent API requests");
                    _logger?.LogError("  2. Implementing longer delays between requests");
                    _logger?.LogError("  3. Processing conversations in smaller batches");
                    _logger?.LogError("  4. Contacting Genesys Cloud support to review API rate limits");
                }
            }
            else
            {
                _logger?.LogInformation("ParticipantAttributes:Summary: Successfully provided participant attributes for all {UniqueConversations} unique conversations ({NewlyProcessed} newly processed, {FromExisting} from existing data). Total input conversations: {TotalInput}",
                    expectedConversations, NewParticipantAttributes.Rows.Count, ParticipantAttributes.Rows.Count - NewParticipantAttributes.Rows.Count, conversationIds.Count);
            }

            return ParticipantAttributes;
        }

        private async Task ProcessParticipantAttributesAsync(DataTable SmallConvTable, DataTable ParticipantAttributes, string URI, string GCApiKey, int MaxNVarCharLength)
        {
            const int BATCH_SIZE = 275; // Increased batch size for better throughput
            const int MAX_CONCURRENT_BATCHES = 8; // Limit concurrent batches to prevent overwhelming the API

            var conversationRows = SmallConvTable.Rows.Cast<DataRow>().ToList();
            _logger?.LogInformation("Processing {TotalCount} conversations for participant attributes in async batches of {BatchSize} (max {MaxConcurrent} concurrent batches, each batch gets fresh token)",
                conversationRows.Count, BATCH_SIZE, MAX_CONCURRENT_BATCHES);

            // Create a semaphore to limit concurrent batches
            using var throttler = new SemaphoreSlim(MAX_CONCURRENT_BATCHES);
            var processingTasks = new List<Task>();

            // Reset unified conversation tracking for this processing run
            _processedConversationIds.Clear();
            _totalUniqueConversationsProcessed = 0;
            var lockObject = new object();

            // Process conversations in batches
            for (int i = 0; i < conversationRows.Count; i += BATCH_SIZE)
            {
                var batch = conversationRows.Skip(i).Take(BATCH_SIZE).ToList();

                // Wait for a slot to become available
                await throttler.WaitAsync();

                // Create a task for this batch
                processingTasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        // Get a fresh token for each batch to ensure optimal performance
                        string batchApiKey = await GetFreshTokenForBatch(GCApiKey, i / BATCH_SIZE + 1);
                        await ProcessConversationBatch(batch, ParticipantAttributes, URI, batchApiKey, MaxNVarCharLength, lockObject);

                        // Progress tracking is now handled by unified conversation tracking in ProcessConversationBatch
                        // Log progress every 500 conversations to reduce log noise
                        int currentCount = _totalUniqueConversationsProcessed;
                        if (currentCount % 500 == 0 || currentCount >= conversationRows.Count)
                        {
                            _logger?.LogInformation("ParticipantAttributes:Progress: Processed {ProcessedCount} unique conversations from {TotalCount} total ({Percent:P0})",
                                currentCount, conversationRows.Count, (double)currentCount / conversationRows.Count);
                        }
                    }
                    finally
                    {
                        throttler.Release();
                    }
                }));
            }

            // Wait for all batches to complete
            await Task.WhenAll(processingTasks);

            // CRITICAL VALIDATION: Ensure keyid and conversationid match for all rows
            await ValidateParticipantAttributesIntegrity(ParticipantAttributes);

            _logger?.LogInformation("ParticipantAttributes:AsyncComplete: Finished processing all {TotalCount} conversations", conversationRows.Count);
        }

        /// <summary>
        /// Validates that keyid and conversationid match for all rows in ParticipantAttributes
        /// CRITICAL: If any mismatch is found, exits the entire process to prevent data corruption
        /// </summary>
        private async Task ValidateParticipantAttributesIntegrity(DataTable participantAttributes)
        {
            try
            {
                _logger?.LogInformation("ParticipantAttributes:Validation: Starting integrity validation for {RowCount} rows", participantAttributes.Rows.Count);

                int validatedRows = 0;
                int mismatchCount = 0;
                var mismatches = new List<string>();

                foreach (DataRow row in participantAttributes.Rows)
                {
                    string keyid = row["keyid"]?.ToString() ?? "";
                    string conversationid = row["conversationid"]?.ToString() ?? "";

                    if (!string.IsNullOrEmpty(keyid) && !string.IsNullOrEmpty(conversationid))
                    {
                        if (keyid != conversationid)
                        {
                            mismatchCount++;
                            mismatches.Add($"Row {validatedRows + 1}: keyid='{keyid}' != conversationid='{conversationid}'");

                            // Log first few mismatches for debugging
                            if (mismatchCount <= 5)
                            {
                                _logger?.LogError("ParticipantAttributes:Validation: CRITICAL DATA CORRUPTION DETECTED - keyid '{KeyId}' does not match conversationid '{ConversationId}' in row {RowNumber}",
                                    keyid, conversationid, validatedRows + 1);
                            }
                        }
                    }

                    validatedRows++;
                }

                if (mismatchCount > 0)
                {
                    _logger?.LogError("ParticipantAttributes:Validation: CRITICAL FAILURE - Found {MismatchCount} keyid/conversationid mismatches out of {TotalRows} rows. This indicates data corruption.",
                        mismatchCount, validatedRows);

                    // Log summary of all mismatches for debugging
                    _logger?.LogError("ParticipantAttributes:Validation: Mismatch details: {Mismatches}", string.Join("; ", mismatches.Take(10)));

                    // CRITICAL: Exit the entire process to prevent data corruption
                    throw new InvalidOperationException($"CRITICAL DATA CORRUPTION: Found {mismatchCount} keyid/conversationid mismatches in ParticipantAttributes. " +
                        $"This indicates threading race conditions or data corruption. Process terminated to prevent database corruption. " +
                        $"First mismatch: {mismatches.FirstOrDefault()}");
                }

                _logger?.LogInformation("ParticipantAttributes:Validation: SUCCESS - All {ValidatedRows} rows passed keyid/conversationid integrity validation", validatedRows);
            }
            catch (Exception ex) when (!(ex is InvalidOperationException))
            {
                _logger?.LogError(ex, "ParticipantAttributes:Validation: Unexpected error during integrity validation");
                throw new InvalidOperationException("CRITICAL: ParticipantAttributes integrity validation failed due to unexpected error. Process terminated to prevent potential data corruption.", ex);
            }
        }

        /// <summary>
        /// Re-processes missing conversations that failed during the initial participant attributes processing
        /// This method attempts to recover from transient failures by re-processing only the conversations that are missing
        /// </summary>
        private async Task ReprocessMissingConversations(List<string> missingIds, DataTable SmallConvTable, DataTable ParticipantAttributes, string URI, string GCApiKey, int MaxNVarCharLength)
        {
            if (!missingIds.Any())
            {
                _logger?.LogInformation("ReprocessMissing: No missing conversations to re-process");
                return;
            }

            _logger?.LogWarning("ReprocessMissing: Starting re-processing of {MissingCount} missing conversations", missingIds.Count);

            try
            {
                // Create a DataTable containing only the missing conversations
                var missingConversationsTable = SmallConvTable.Clone();
                missingConversationsTable.TableName = "MissingConversations";

                foreach (var missingId in missingIds)
                {
                    var missingRow = SmallConvTable.AsEnumerable().FirstOrDefault(row =>
                        row["conversationid"] != null &&
                        row["conversationid"] != System.DBNull.Value &&
                        row["conversationid"].ToString() == missingId);

                    if (missingRow != null)
                    {
                        missingConversationsTable.ImportRow(missingRow);
                    }
                }

                _logger?.LogInformation("ReprocessMissing: Created table with {MissingTableCount} missing conversations for re-processing", missingConversationsTable.Rows.Count);

                // Clear the processed conversation tracking for these specific conversations to allow re-processing
                foreach (var missingId in missingIds)
                {
                    _processedParticipantAttributeConversations.TryRemove(missingId, out _);
                }

                // Re-process the missing conversations using the same logic as the main processing
                // Use smaller batch size and reduced concurrency for better reliability during retry
                const int RETRY_BATCH_SIZE = 50; // Smaller batches for better reliability
                const int MAX_RETRY_CONCURRENT_BATCHES = 2; // Reduced concurrency for stability

                var conversationRows = missingConversationsTable.Rows.Cast<DataRow>().ToList();
                _logger?.LogInformation("ReprocessMissing: Re-processing {TotalCount} missing conversations in smaller batches of {BatchSize} (max {MaxConcurrent} concurrent batches)",
                    conversationRows.Count, RETRY_BATCH_SIZE, MAX_RETRY_CONCURRENT_BATCHES);

                // Create a semaphore to limit concurrent batches during retry
                using var throttler = new SemaphoreSlim(MAX_RETRY_CONCURRENT_BATCHES);
                var processingTasks = new List<Task>();
                var lockObject = new object();

                // Process missing conversations in smaller batches
                for (int i = 0; i < conversationRows.Count; i += RETRY_BATCH_SIZE)
                {
                    var batch = conversationRows.Skip(i).Take(RETRY_BATCH_SIZE).ToList();

                    // Wait for a slot to become available
                    await throttler.WaitAsync();

                    // Create a task for this batch
                    processingTasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            // Get a fresh token for each retry batch
                            string retryApiKey = await GetFreshTokenForBatch(GCApiKey, i / RETRY_BATCH_SIZE + 1);
                            await ProcessConversationBatch(batch, ParticipantAttributes, URI, retryApiKey, MaxNVarCharLength, lockObject);

                            _logger?.LogInformation("ReprocessMissing: Successfully processed retry batch {BatchNumber} with {BatchSize} conversations",
                                i / RETRY_BATCH_SIZE + 1, batch.Count);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, "ReprocessMissing: Error processing retry batch {BatchNumber}", i / RETRY_BATCH_SIZE + 1);
                            // Don't throw - allow other batches to continue
                        }
                        finally
                        {
                            throttler.Release();
                        }
                    }));
                }

                // Wait for all retry batches to complete
                await Task.WhenAll(processingTasks);

                // Verify how many missing conversations were recovered
                var recoveredIds = missingIds.Where(id =>
                    ParticipantAttributes.AsEnumerable().Any(row =>
                        row.Field<string>("conversationid") == id)).ToList();

                int recoveredCount = recoveredIds.Count;
                int stillMissingCount = missingIds.Count - recoveredCount;

                if (recoveredCount > 0)
                {
                    _logger?.LogInformation("ReprocessMissing: SUCCESS - Recovered {RecoveredCount} out of {TotalMissing} missing conversations ({RecoveryRate:P1})",
                        recoveredCount, missingIds.Count, (double)recoveredCount / missingIds.Count);
                }

                if (stillMissingCount > 0)
                {
                    var stillMissingIds = missingIds.Except(recoveredIds).Take(5).ToList();
                    _logger?.LogWarning("ReprocessMissing: {StillMissingCount} conversations still missing after retry. Sample IDs: {StillMissingIds}",
                        stillMissingCount, string.Join(", ", stillMissingIds));
                }
                else
                {
                    _logger?.LogInformation("ReprocessMissing: COMPLETE SUCCESS - All missing conversations recovered!");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "ReprocessMissing: Critical error during missing conversation re-processing");
                // Don't throw - this is a recovery attempt, main processing should continue
            }
        }

        /// <summary>
        /// Processes conversation details query pages concurrently for optimal performance
        /// </summary>
        private async Task ProcessConversationDetailsPagesAsync(string URI, string GCApiKey, string StartDate, string EndDate, int totalPages,
            DataTable DetailInteraction, DataTable ParticipantSummary, DataTable FlowOutcomes, TimeZoneInfo AppTimeZone)
        {
            const int MAX_CONCURRENT_PAGES = 5; // Limit concurrent pages to prevent overwhelming the API
            using var throttler = new SemaphoreSlim(MAX_CONCURRENT_PAGES);
            var processingTasks = new List<Task>();
            var lockObject = new object();

            _logger?.LogInformation("ConversationDetails: Processing {TotalPages} pages concurrently (max {MaxConcurrent} concurrent pages)", totalPages, MAX_CONCURRENT_PAGES);

            for (int pageNumber = 1; pageNumber <= totalPages; pageNumber++)
            {
                // Wait for a slot to become available
                await throttler.WaitAsync();

                // Create a task for this page
                processingTasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        await ProcessSingleConversationDetailsPage(URI, GCApiKey, StartDate, EndDate, pageNumber,
                            DetailInteraction, ParticipantSummary, FlowOutcomes, AppTimeZone, lockObject);
                    }
                    finally
                    {
                        throttler.Release(); // Always release the semaphore slot
                    }
                }));
            }

            // Wait for all pages to complete
            await Task.WhenAll(processingTasks);
            _logger?.LogInformation("ConversationDetails: Completed processing all {TotalPages} pages concurrently", totalPages);
        }

        /// <summary>
        /// Processes conversation details query pages concurrently for FlowOutcome data only
        /// </summary>
        private async Task ProcessFlowOutcomePagesAsync(string URI, string GCApiKey, string StartDate, string EndDate, int totalPages, DataTable FlowOutcomes)
        {
            const int MAX_CONCURRENT_PAGES = 5; // Limit concurrent pages to prevent overwhelming the API
            using var throttler = new SemaphoreSlim(MAX_CONCURRENT_PAGES);
            var processingTasks = new List<Task>();
            var lockObject = new object();

            _logger?.LogInformation("FlowOutcome: Processing {TotalPages} pages concurrently (max {MaxConcurrent} concurrent pages)", totalPages, MAX_CONCURRENT_PAGES);

            for (int pageNumber = 1; pageNumber <= totalPages; pageNumber++)
            {
                // Wait for a slot to become available
                await throttler.WaitAsync();

                // Create a task for this page
                processingTasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        await ProcessSingleFlowOutcomePage(URI, GCApiKey, StartDate, EndDate, pageNumber, FlowOutcomes, lockObject);
                    }
                    finally
                    {
                        throttler.Release(); // Always release the semaphore slot
                    }
                }));
            }

            // Wait for all pages to complete
            await Task.WhenAll(processingTasks);
            _logger?.LogInformation("FlowOutcome: Completed processing all {TotalPages} pages concurrently", totalPages);
        }

        /// <summary>
        /// Processes a single page of conversation details query for all data types
        /// </summary>
        private async Task ProcessSingleConversationDetailsPage(string URI, string GCApiKey, string StartDate, string EndDate, int pageNumber,
            DataTable DetailInteraction, DataTable ParticipantSummary, DataTable FlowOutcomes, TimeZoneInfo AppTimeZone, object lockObject)
        {
            try
            {
                _logger?.LogDebug("Processing page {PageNumber} for conversation details query", pageNumber);

                string RequestBody = "{" +
                                " \"interval\": \"" + StartDate + "/" + EndDate + "\", " +
                                " \"order\": \"asc\"," +
                                " \"orderBy\": \"conversationEnd\"," +
                                " \"flattenMultivaluedDimensions\": true," +
                                " \"dimensions\": [\"conversationId\", \"participantId\", \"sessionId\", \"flowId\", \"flowName\", \"flowType\", \"flowVersion\", \"flowOutcomeId\", \"flowOutcomeName\", \"flowOutcomeValue\"]," +
                                " \"paging\": {" +
                                " \"pageSize\": 100," +
                                "  \"pageNumber\": " + pageNumber.ToString() +
                                "}}";

                _logger?.LogDebug("Conversation details query request body: {RequestBody}", RequestBody);



                string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/analytics/conversations/details/query", GCApiKey, RequestBody);

                // Check for null or empty response to prevent NullReferenceException
                if (string.IsNullOrEmpty(JsonString) || JsonString.Length < 50)
                {
                    _logger?.LogWarning("ConversationDetails: Page {PageNumber} received null or empty response from API", pageNumber);
                    return;
                }

                // Process the JSON response with full conversation data processing
                try
                {
                    // Enhanced debugging for the specific error location
                    _logger?.LogDebug("About to call ProcessConversationDetailsPageData for page {PageNumber}", pageNumber);

                    // Validate parameters before calling the method
                    if (string.IsNullOrEmpty(JsonString))
                    {
                        _logger?.LogError("JsonString is null or empty for page {PageNumber}", pageNumber);
                        throw new ArgumentNullException(nameof(JsonString), "JsonString cannot be null or empty");
                    }

                    if (DetailInteraction == null)
                    {
                        _logger?.LogError("DetailInteraction DataTable is null for page {PageNumber}", pageNumber);
                        throw new ArgumentNullException(nameof(DetailInteraction), "DetailInteraction DataTable cannot be null");
                    }

                    if (ParticipantSummary == null)
                    {
                        _logger?.LogError("ParticipantSummary DataTable is null for page {PageNumber}", pageNumber);
                        throw new ArgumentNullException(nameof(ParticipantSummary), "ParticipantSummary DataTable cannot be null");
                    }

                    if (FlowOutcomes == null)
                    {
                        _logger?.LogError("FlowOutcomes DataTable is null for page {PageNumber}", pageNumber);
                        throw new ArgumentNullException(nameof(FlowOutcomes), "FlowOutcomes DataTable cannot be null");
                    }

                    if (AppTimeZone == null)
                    {
                        _logger?.LogError("AppTimeZone is null for page {PageNumber}", pageNumber);
                        throw new ArgumentNullException(nameof(AppTimeZone), "AppTimeZone cannot be null");
                    }

                    if (lockObject == null)
                    {
                        _logger?.LogError("lockObject is null for page {PageNumber}", pageNumber);
                        throw new ArgumentNullException(nameof(lockObject), "lockObject cannot be null");
                    }

                    _logger?.LogDebug("All parameters validated successfully for page {PageNumber}, calling ProcessConversationDetailsPageData", pageNumber);

                    await ProcessConversationDetailsPageData(JsonString, DetailInteraction, ParticipantSummary, FlowOutcomes, AppTimeZone, lockObject, pageNumber);

                    _logger?.LogDebug("ProcessConversationDetailsPageData completed successfully for page {PageNumber}", pageNumber);
                }
                catch (Exception innerEx)
                {
                    _logger?.LogError(innerEx, "Error in ProcessConversationDetailsPageData call for page {PageNumber}. Inner exception: {InnerException}",
                        pageNumber, innerEx.InnerException?.Message ?? "None");
                    throw; // Re-throw the inner exception
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "ConversationDetails: Error processing page {PageNumber}. Exception type: {ExceptionType}, Message: {Message}",
                    pageNumber, ex.GetType().Name, ex.Message);

                // Log additional context information
                _logger?.LogError("ConversationDetails: Stack trace for page {PageNumber}: {StackTrace}", pageNumber, ex.StackTrace);

                throw; // Re-throw to ensure the main process is aware of the failure
            }
        }

        /// <summary>
        /// Processes a single page of conversation details query for FlowOutcome data only
        /// </summary>
        private async Task ProcessSingleFlowOutcomePage(string URI, string GCApiKey, string StartDate, string EndDate, int pageNumber, DataTable FlowOutcomes, object lockObject)
        {
            try
            {
                _logger?.LogDebug("Processing page {PageNumber} for conversation details query", pageNumber);

                string RequestBody = "{" +
                                " \"interval\": \"" + StartDate + "/" + EndDate + "\", " +
                                " \"order\": \"asc\"," +
                                " \"orderBy\": \"conversationEnd\"," +
                                " \"flattenMultivaluedDimensions\": true," +
                                " \"dimensions\": [\"conversationId\", \"participantId\", \"sessionId\", \"flowId\", \"flowName\", \"flowType\", \"flowVersion\", \"flowOutcomeId\", \"flowOutcomeName\", \"flowOutcomeValue\"]," +
                                " \"paging\": {" +
                                " \"pageSize\": 100," +
                                "  \"pageNumber\": " + pageNumber.ToString() +
                                "}}";

                _logger?.LogDebug("Conversation details query request body: {RequestBody}", RequestBody);

                string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/analytics/conversations/details/query", GCApiKey, RequestBody);

                // Check for null or empty response to prevent NullReferenceException
                if (string.IsNullOrEmpty(JsonString) || JsonString.Length < 50)
                {
                    _logger?.LogWarning("FlowOutcome: Page {PageNumber} received null or empty response from API", pageNumber);
                    return;
                }

                // Process the JSON response (continuing with existing logic)
                // Use UTC timezone as default for FlowOutcome processing
                await ProcessFlowOutcomePageData(JsonString, FlowOutcomes, lockObject, pageNumber, TimeZoneInfo.Utc);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "FlowOutcome: Error processing page {PageNumber}", pageNumber);
                throw; // Re-throw to ensure the main process is aware of the failure
            }
        }

        /// <summary>
        /// Processes the JSON data from a single page of conversation details query for all data types
        /// </summary>
        private async Task ProcessConversationDetailsPageData(string JsonString, DataTable DetailInteraction, DataTable ParticipantSummary,
            DataTable FlowOutcomes, TimeZoneInfo AppTimeZone, object lockObject, int pageNumber)
        {

            try
            {
                _logger?.LogDebug("Starting ProcessConversationDetailsPageData for page {PageNumber}", pageNumber);

                // Enhanced JSON deserialization with better error handling
                Interactions.InteractionSegmentStruct DetailData = null;
                try
                {
                    _logger?.LogDebug("Attempting JSON deserialization for page {PageNumber}, JSON length: {JsonLength}", pageNumber, JsonString?.Length ?? 0);

                    DetailData = JsonConvert.DeserializeObject<Interactions.InteractionSegmentStruct>(JsonString,
                                   new JsonSerializerSettings
                                   {
                                       NullValueHandling = NullValueHandling.Ignore
                                   });

                    _logger?.LogDebug("JSON deserialization successful for page {PageNumber}", pageNumber);
                }
                catch (JsonException jsonEx)
                {
                    _logger?.LogError(jsonEx, "JSON deserialization failed for page {PageNumber}. JSON content preview: {JsonPreview}",
                        pageNumber, JsonString?.Substring(0, Math.Min(500, JsonString?.Length ?? 0)) ?? "NULL");
                    throw new InvalidOperationException($"Failed to deserialize JSON response for page {pageNumber}", jsonEx);
                }
                catch (Exception deserEx)
                {
                    _logger?.LogError(deserEx, "Unexpected error during JSON deserialization for page {PageNumber}", pageNumber);
                    throw;
                }

                if (DetailData?.conversations == null)
                {
                    _logger?.LogWarning("ConversationDetails: Page {PageNumber} contains no conversations", pageNumber);
                    return;
                }

                bool timeFieldsMillisecondResolution = DetailInteraction.Columns["segmenttime"].DataType == typeof(System.Decimal);
                int conversationsProcessed = 0;
                int totalConversations = DetailData.conversations?.Length ?? 0;

                _logger?.LogDebug("Processing {TotalConversations} conversations for page {PageNumber}", totalConversations, pageNumber);



                foreach (Interactions.Conversation Conv in DetailData.conversations)
                {
                    try
                    {
                        string currentConvId = Conv?.conversationId ?? "NULL";
                        bool isTargetConversation = currentConvId == "f3033d65-4ee9-4d50-96f8-8e87eb4ce3a0";

                        _logger?.LogTrace("Processing conversation {ConversationId} ({ProcessedCount}/{TotalCount}) on page {PageNumber}",
                            currentConvId, conversationsProcessed + 1, totalConversations, pageNumber);

                    int PartCode = 0;
                    int SessCode = 0;
                    int SegCode = 0;

                    foreach (Interactions.Participant ConvPart in Conv.participants)
                    {
                        // Create participant key for efficient duplicate detection
                        string participantKey = $"{Conv.conversationId}|{ConvPart.participantId}";

                        // Use thread-safe ConcurrentDictionary for efficient duplicate checking
                        // This avoids SQL injection risks and improves performance over DataTable.Select()
                        if (!_processedParticipantKeys.TryAdd(participantKey, true))
                        {
                            // Participant already processed, skip to avoid duplicates
                            continue;
                        }

                        DataRow DRPartSumm = null;
                        lock (lockObject)
                        {
                            DRPartSumm = ParticipantSummary.NewRow();
                        }

                        DRPartSumm["keyid"] = participantKey;
                        DRPartSumm["conversationid"] = Conv.conversationId;
                        DRPartSumm["participantid"] = ConvPart.participantId;
                        // Handle null purpose value to prevent NoNullAllowedException
                        DRPartSumm["purpose"] = !string.IsNullOrEmpty(ConvPart.purpose) ? ConvPart.purpose : "unknown";

                        if (Conv.divisionIds.Count() > 0 && !string.IsNullOrEmpty(Conv.divisionIds[0]))
                        {
                            DRPartSumm["divisionid"] = Conv.divisionIds[0];
                        }
                        else
                        {
                            DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                        }

                        if (DRPartSumm["divisionid"] == null || DRPartSumm["divisionid"] == System.DBNull.Value|| DRPartSumm["divisionid"] is DBNull)
                        {
                            _logger?.LogDebug("Setting default division ID for participant summary in query method");
                            DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                        }
                        if (Conv.divisionIds.Count() > 1 && !string.IsNullOrEmpty(Conv.divisionIds[1]))
                            DRPartSumm["divisionid2"] = Conv.divisionIds[1];

                        if (Conv.divisionIds.Count() > 2 && !string.IsNullOrEmpty(Conv.divisionIds[2]))
                            DRPartSumm["divisionid3"] = Conv.divisionIds[2];

                        // Conv.conversationStart is already UTC from API deserialization
                        // Validate conversation start date - throw if invalid to maintain data integrity
                        if (Conv.conversationStart == default(DateTime))
                        {
                            throw new InvalidDataException($"Invalid conversation start date {Conv.conversationStart} for conversation {Conv.conversationId}. Cannot process conversation with invalid date.");
                        }

                        DRPartSumm["conversationstartdate"] = Conv.conversationStart;
                        DRPartSumm["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart, AppTimeZone);

                        if (Conv.conversationEnd > DateTime.UtcNow.AddYears(-20))
                        {
                            // Conv.conversationEnd is already UTC from API deserialization
                            DRPartSumm["conversationenddate"] = Conv.conversationEnd;
                            DRPartSumm["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd, AppTimeZone);
                        }
                        else
                        {
                            DRPartSumm["conversationenddate"] = System.DBNull.Value;
                            DRPartSumm["conversationenddateltc"] = System.DBNull.Value;
                        }

                        PartCode++;
                        SessCode = 0;
                        SegCode = 0;

                        // Continue with session processing...
                        await ProcessParticipantSessions(Conv, ConvPart, DRPartSumm, DetailInteraction, FlowOutcomes, AppTimeZone,
                            lockObject, timeFieldsMillisecondResolution, PartCode);

                        // Add participant summary row (duplicate checking already done above)
                        lock (_participantSummaryLock)
                        {
                            ParticipantSummary.Rows.Add(DRPartSumm);
                        }
                    }
                    }
                    catch (Exception convEx)
                    {
                        string conversationId = Conv?.conversationId ?? "Unknown";
                        _logger?.LogError(convEx, "Error processing conversation {ConversationId} on page {PageNumber}", conversationId, pageNumber);
                        // Continue processing other conversations instead of failing the entire page
                    }
                    conversationsProcessed++;
                }

                _logger?.LogDebug("ConversationDetails: Page {PageNumber} processed {ConversationCount} conversations", pageNumber, conversationsProcessed);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "ConversationDetails: Error processing JSON data for page {PageNumber}", pageNumber);
                throw;
            }
        }

        /// <summary>
        /// Processes participant sessions for a single participant in concurrent processing
        /// </summary>
        private async Task ProcessParticipantSessions(Interactions.Conversation Conv, Interactions.Participant ConvPart, DataRow DRPartSumm,
            DataTable DetailInteraction, DataTable FlowOutcomes, TimeZoneInfo AppTimeZone, object lockObject,
            bool timeFieldsMillisecondResolution, int PartCode)
        {
            try
            {
                bool isTargetConversation = Conv.conversationId == "f3033d65-4ee9-4d50-96f8-8e87eb4ce3a0";
                int SessCode = 0;
                int SegCode = 0;

                // Add null safety check for sessions collection
                if (ConvPart.sessions == null)
                {
                    _logger?.LogWarning("Sessions collection is null for participant {ParticipantId} in conversation {ConversationId}. Skipping session processing.",
                        ConvPart.participantId, Conv.conversationId);
                    return;
                }

                foreach (Interactions.Session ConvSess in ConvPart.sessions)
                {
                    // Add null safety check for individual session
                    if (ConvSess == null)
                    {
                        _logger?.LogWarning("Null session found for participant {ParticipantId} in conversation {ConversationId}. Skipping session.",
                            ConvPart.participantId, Conv.conversationId);
                        continue;
                    }

                    SessCode++;
                    SegCode = 0;
                    Interactions.Flow ConvSessFlow = ConvSess.flow;

                    // Add null safety check for segments collection
                    if (ConvSess.segments == null)
                    {
                        _logger?.LogWarning("Segments collection is null for session in participant {ParticipantId} in conversation {ConversationId}. Skipping segment processing.",
                            ConvPart.participantId, Conv.conversationId);
                        continue;
                    }

                    int SegmentCount = ConvSess.segments.Length;
                    int CurrentSegment = 1;

                    foreach (Interactions.Segment ConvSeg in ConvSess.segments)
                    {
                        // Add null safety check for individual segment
                        if (ConvSeg == null)
                        {
                            _logger?.LogWarning("Null segment found for session in participant {ParticipantId} in conversation {ConversationId}. Skipping segment.",
                                ConvPart.participantId, Conv.conversationId);
                            continue;
                        }

                        SegCode++;

                        if (!timeFieldsMillisecondResolution)
                        {
                            ConvSeg.segmentStart = new DateTime(
                                  ConvSeg.segmentStart.Ticks - (ConvSeg.segmentStart.Ticks % TimeSpan.TicksPerSecond),
                                  ConvSeg.segmentStart.Kind
                                );

                            ConvSeg.segmentEnd = new DateTime(
                                ConvSeg.segmentEnd.Ticks - (ConvSeg.segmentEnd.Ticks % TimeSpan.TicksPerSecond),
                                ConvSeg.segmentEnd.Kind
                             );
                        }

                        string IterationCode = PartCode.ToString() + "|" + SessCode.ToString() + "|" + SegCode.ToString();

                        // THREADING FIX: Collect all row data first, then create and add row entirely within lock
                        // Validate conversation start date - throw if invalid to maintain data integrity
                        if (Conv.conversationStart == default(DateTime))
                        {
                            throw new InvalidDataException($"Invalid conversation start date {Conv.conversationStart} for conversation {Conv.conversationId}. Cannot process conversation with invalid date.");
                        }

                        // Collect all the data we need for the row
                        string TempKeyid = Conv.conversationId + "|" + IterationCode;
                        string keyId = Conv.conversationId + "|ID:" + UCAUtils.GetStableHashCode(TempKeyid);

                        // Prepare division IDs
                        string divisionId = "00000000-0000-0000-0000-0000000000000";
                        string divisionId2 = "";
                        string divisionId3 = "";

                        if (Conv.divisionIds != null && Conv.divisionIds.Count() > 0 && !string.IsNullOrEmpty(Conv.divisionIds[0]))
                        {
                            divisionId = Conv.divisionIds[0];
                        }
                        if (Conv.divisionIds != null && Conv.divisionIds.Count() > 1 && !string.IsNullOrEmpty(Conv.divisionIds[1]))
                            divisionId2 = Conv.divisionIds[1];
                        if (Conv.divisionIds != null && Conv.divisionIds.Count() > 2 && !string.IsNullOrEmpty(Conv.divisionIds[2]))
                            divisionId3 = Conv.divisionIds[2];

                        // Prepare conversation dates
                        DateTime conversationStartDate = Conv.conversationStart;
                        DateTime conversationStartDateLtc = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart, AppTimeZone);

                        object conversationEndDate = System.DBNull.Value;
                        object conversationEndDateLtc = System.DBNull.Value;
                        if (Conv.conversationEnd > DateTime.UtcNow.AddYears(-20))
                        {
                            conversationEndDate = Conv.conversationEnd;
                            conversationEndDateLtc = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd, AppTimeZone);
                        }

                        // Prepare other fields
                        string participantName = ConvPart.participantName;
                        if (participantName != null && participantName.Length > 250)
                            participantName = participantName.Substring(0, 250);

                        string purpose = !string.IsNullOrEmpty(ConvPart.purpose) ? ConvPart.purpose : "unknown";

                        string ani = ConvSess.ani;
                        if (ani != null && ani.Length > 300)
                            ani = ani.Substring(0, 300);

                        string dnis = ConvSess.dnis;
                        if (dnis != null && dnis.Length > 300)
                            dnis = dnis.Substring(0, 300);

                        string sessionDnis = ConvSess.sessionDnis;
                        if (sessionDnis != null && sessionDnis.Length > 300)
                            sessionDnis = sessionDnis.Substring(0, 300);

                        string remoteDisplayable = ConvSess.remote;
                        if (remoteDisplayable != null && remoteDisplayable.Length > 250)
                            remoteDisplayable = remoteDisplayable.Substring(0, 249);

                        // Create a temporary row data structure to pass to ProcessSegmentData
                        var rowData = new Dictionary<string, object>
                        {
                            ["keyid"] = keyId,
                            ["conversationid"] = Conv.conversationId,
                            ["divisionid"] = divisionId,
                            ["divisionid2"] = divisionId2,
                            ["divisionid3"] = divisionId3,
                            ["conversationstartdate"] = conversationStartDate,
                            ["conversationstartdateltc"] = conversationStartDateLtc,
                            ["conversationenddate"] = conversationEndDate,
                            ["conversationenddateltc"] = conversationEndDateLtc,
                            ["gencode"] = IterationCode,
                            ["peer"] = ConvSess.peerId,
                            ["conversationminmos"] = decimal.Round(Conv.mediaStatsMinConversationMos, 2),
                            ["originaldirection"] = Conv.originatingDirection,
                            ["participantid"] = ConvPart.participantId,
                            ["participantname"] = participantName,
                            ["purpose"] = purpose,
                            ["mediatype"] = ConvSess.mediaType,
                            ["ani"] = ani,
                            ["queueid"] = ConvSeg.queueId,
                            ["userid"] = ConvPart.userId,
                            ["dnis"] = dnis,
                            ["sessiondnis"] = sessionDnis,
                            ["sessiondirection"] = ConvSess.direction,
                            ["edgeId"] = ConvSess.edgeId,
                            ["remotedisplayable"] = remoteDisplayable,
                            ["conversationminrfactor"] = decimal.Round(Conv.mediaStatsMinConversationRFactor, 2),
                            ["externalTag"] = Conv.externalTag
                        };

                        // Process segment data and collect additional fields
                        await ProcessSegmentDataThreadSafe(Conv, ConvSeg, ConvSess, ConvSessFlow, rowData, DRPartSumm, FlowOutcomes, AppTimeZone,
                            lockObject, CurrentSegment, SegmentCount);

                        // Update DRPartSumm fields that are set in this method
                        DRPartSumm["mediaType"] = ConvSess.mediaType;
                        if (ConvSeg.queueId != null)
                            DRPartSumm["queueid"] = ConvSeg.queueId;
                        if (ConvPart.userId != null)
                            DRPartSumm["userid"] = ConvPart.userId;

                        // Update DetailInteractionLastUpdate
                        DateTime MaxDateTest = Conv.conversationStart;
                        if (MaxDateTest > DetailInteractionLastUpdate)
                        {
                            DetailInteractionLastUpdate = MaxDateTest;
                            // Updated last interaction date
                        }

                        // THREADING FIX: Create and add row entirely within lock to prevent race conditions
                        if (!string.IsNullOrEmpty(keyId) && _processedDetailInteractionKeys.TryAdd(keyId, true))
                        {
                            lock (_detailInteractionLock)
                            {
                                DataRow NewRow = DetailInteraction.NewRow();

                                // Set all the collected data on the row
                                foreach (var kvp in rowData)
                                {
                                    if (DetailInteraction.Columns.Contains(kvp.Key))
                                    {
                                        NewRow[kvp.Key] = kvp.Value ?? System.DBNull.Value;
                                    }
                                }

                                DetailInteraction.Rows.Add(NewRow);
                            }
                        }

                        CurrentSegment++;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing participant sessions for conversation {ConversationId}. Participant ID: {ParticipantId}. ConvStart: {ConversationStart}. ConvEnd {ConversationEnd}. Error: {ErrorMessage}",
                    Conv?.conversationId ?? "Unknown", ConvPart?.participantId ?? "Unknown", Conv?.conversationStart, Conv?.conversationEnd, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Processes segment data including timing, flow outcomes, and metrics (thread-safe version)
        /// </summary>
        private async Task ProcessSegmentDataThreadSafe(Interactions.Conversation Conv, Interactions.Segment ConvSeg, Interactions.Session ConvSess,
            Interactions.Flow ConvSessFlow, Dictionary<string, object> rowData, DataRow DRPartSumm, DataTable FlowOutcomes, TimeZoneInfo AppTimeZone,
            object lockObject, int CurrentSegment, int SegmentCount)
        {
            try
            {
                // ConvSeg.segmentStart is already UTC from API deserialization
                var segmentStartUtc = ConvSeg.segmentStart;
                rowData["segmentstartdate"] = segmentStartUtc;
                rowData["segmentstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(segmentStartUtc, AppTimeZone);

                TimeSpan Diff = new TimeSpan();

                if (ConvSeg.segmentEnd > DateTime.UtcNow.AddYears(-20))
                {
                    // ConvSeg.segmentEnd is already UTC from API deserialization
                    var segmentEndUtc = ConvSeg.segmentEnd;
                    rowData["segmentenddate"] = segmentEndUtc;
                    rowData["segmentenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(segmentEndUtc, AppTimeZone);
                    Diff = ConvSeg.segmentEnd - ConvSeg.segmentStart;
                    rowData["segmenttime"] = Diff.TotalSeconds;
                    Diff = ConvSeg.segmentEnd - Conv.conversationStart;
                    rowData["convtosegmentendtime"] = Diff.TotalSeconds;
                }
                else
                {
                    rowData["segmentenddate"] = System.DBNull.Value;
                    rowData["segmenttime"] = System.DBNull.Value;
                    rowData["convtosegmentendtime"] = System.DBNull.Value;
                }

                Diff = ConvSeg.segmentStart - Conv.conversationStart;
                rowData["convtosegmentstarttime"] = Diff.TotalSeconds;

                rowData["segmenttype"] = ConvSeg.segmentType;
                rowData["conference"] = ConvertBoolean(ConvSeg.conference);
                rowData["segdestinationConversationId"] = ConvSeg.destinationConversationId;

                string RowWrapUp = ConvSeg.wrapUpCode;
                string RowWrapUpNote = ConvSeg.wrapUpNote;
                if (RowWrapUp != null)
                {
                    if (RowWrapUp == "ININ-WRAP-UP-TIMEOUT")
                        RowWrapUp = "00000000-0000-0000-0000-0000000000000";
                    rowData["wrapupcode"] = RowWrapUp;
                    DRPartSumm["wrapupcode"] = RowWrapUp;
                    if (RowWrapUpNote != null)
                    {
                        rowData["wrapupnote"] = RowWrapUpNote;
                        DRPartSumm["wrapupnote"] = RowWrapUpNote;
                    }
                    else
                    {
                        rowData["wrapupnote"] = "";
                    }
                }
                else
                {
                    rowData["wrapupcode"] = "";
                    rowData["wrapupnote"] = "";
                }

                if (ConvSeg.disconnectType == null)
                    rowData["disconnectiontype"] = "none";
                else
                    rowData["disconnectiontype"] = ConvSeg.disconnectType;

                rowData["recordingexists"] = ConvertBoolean(ConvSess.recording);
                rowData["sessionprovider"] = ConvSess.provider;

                // Process flow data if this is the last segment
                if (CurrentSegment == SegmentCount && ConvSessFlow != null && ConvSessFlow.flowId != null)
                {
                    await ProcessFlowDataThreadSafe(Conv, ConvSessFlow, rowData, FlowOutcomes, AppTimeZone, lockObject);
                }

                // Process metrics if this is the last segment
                if (CurrentSegment == SegmentCount && ConvSess.metrics != null && ConvSess.metrics.Length > 0)
                {
                    await ProcessMetricsThreadSafe(ConvSess.metrics, rowData, DRPartSumm, Conv);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing segment data for conversation {ConversationId}", Conv.conversationId);
                throw;
            }
        }

        /// <summary>
        /// Processes segment data including timing, flow outcomes, and metrics
        /// </summary>
        private async Task ProcessSegmentData(Interactions.Conversation Conv, Interactions.Segment ConvSeg, Interactions.Session ConvSess,
            Interactions.Flow ConvSessFlow, DataRow NewRow, DataRow DRPartSumm, DataTable FlowOutcomes, TimeZoneInfo AppTimeZone,
            object lockObject, int CurrentSegment, int SegmentCount)
        {
            try
            {
                // ConvSeg.segmentStart is already UTC from API deserialization
                var segmentStartUtc = ConvSeg.segmentStart;
                NewRow["segmentstartdate"] = segmentStartUtc;
                NewRow["segmentstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(segmentStartUtc, AppTimeZone);

                TimeSpan Diff = new TimeSpan();

                if (ConvSeg.segmentEnd > DateTime.UtcNow.AddYears(-20))
                {
                    // ConvSeg.segmentEnd is already UTC from API deserialization
                    var segmentEndUtc = ConvSeg.segmentEnd;
                    NewRow["segmentenddate"] = segmentEndUtc;
                    NewRow["segmentenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(segmentEndUtc, AppTimeZone);
                    Diff = ConvSeg.segmentEnd - ConvSeg.segmentStart;
                    NewRow["segmenttime"] = Diff.TotalSeconds;
                    Diff = ConvSeg.segmentEnd - Conv.conversationStart;
                    NewRow["convtosegmentendtime"] = Diff.TotalSeconds;
                }
                else
                {
                    NewRow["segmentenddate"] = System.DBNull.Value;
                    NewRow["segmenttime"] = System.DBNull.Value;
                    NewRow["convtosegmentendtime"] = System.DBNull.Value;
                }

                Diff = ConvSeg.segmentStart - Conv.conversationStart;
                NewRow["convtosegmentstarttime"] = Diff.TotalSeconds;

                NewRow["segmenttype"] = ConvSeg.segmentType;
                NewRow["conference"] = ConvertBoolean(ConvSeg.conference);
                NewRow["segdestinationConversationId"] = ConvSeg.destinationConversationId;

                string RowWrapUp = ConvSeg.wrapUpCode;
                string RowWrapUpNote = ConvSeg.wrapUpNote;
                if (RowWrapUp != null)
                {
                    if (RowWrapUp == "ININ-WRAP-UP-TIMEOUT")
                        RowWrapUp = "00000000-0000-0000-0000-0000000000000";
                    NewRow["wrapupcode"] = RowWrapUp;
                    DRPartSumm["wrapupcode"] = RowWrapUp;
                    if (RowWrapUpNote != null)
                    {
                        NewRow["wrapupnote"] = RowWrapUpNote;
                        DRPartSumm["wrapupnote"] = RowWrapUpNote;
                    }
                    else
                    {
                        NewRow["wrapupnote"] = "";
                    }
                }
                else
                {
                    NewRow["wrapupcode"] = "";
                    NewRow["wrapupnote"] = "";
                }

                if (ConvSeg.disconnectType == null)
                    NewRow["disconnectiontype"] = "none";
                else
                    NewRow["disconnectiontype"] = ConvSeg.disconnectType;

                NewRow["recordingexists"] = ConvertBoolean(ConvSess.recording);
                NewRow["sessionprovider"] = ConvSess.provider;

                // Process flow data if this is the last segment
                if (CurrentSegment == SegmentCount && ConvSessFlow != null && ConvSessFlow.flowId != null)
                {
                    await ProcessFlowData(Conv, ConvSessFlow, NewRow, FlowOutcomes, AppTimeZone, lockObject);
                }

                // Process metrics if this is the last segment
                if (CurrentSegment == SegmentCount && ConvSess.metrics != null && ConvSess.metrics.Length > 0)
                {
                    await ProcessMetrics(ConvSess.metrics, NewRow, DRPartSumm, Conv);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing segment data for conversation {ConversationId}", Conv.conversationId);
                throw;
            }
        }

        /// <summary>
        /// Processes flow data and outcomes (thread-safe version)
        /// </summary>
        private async Task ProcessFlowDataThreadSafe(Interactions.Conversation Conv, Interactions.Flow ConvSessFlow, Dictionary<string, object> rowData,
            DataTable FlowOutcomes, TimeZoneInfo AppTimeZone, object lockObject)
        {
            try
            {
                rowData["flowid"] = ConvSessFlow.flowId;
                rowData["flowname"] = ConvSessFlow.flowName;

                try
                {
                    rowData["flowversion"] = decimal.Round(decimal.Parse(ConvSessFlow.flowVersion), 2);
                }
                catch
                {
                    rowData["flowversion"] = 1.0;
                }

                rowData["flowtype"] = ConvSessFlow.flowType;
                rowData["exitreason"] = ConvSessFlow.exitReason;
                rowData["entryreason"] = ConvSessFlow.entryReason;
                rowData["entrytype"] = ConvSessFlow.entryType;
                rowData["transfertype"] = ConvSessFlow.transferType;
                if (ConvSessFlow.transferTargetName != null && ConvSessFlow.transferTargetName.Length > 254)
                    rowData["transfertargetname"] = ConvSessFlow.transferTargetName.Substring(0, 254);
                else
                    rowData["transfertargetname"] = ConvSessFlow.transferTargetName;

                rowData["issuedcallback"] = ConvertBoolean(ConvSessFlow.issuedCallback);

                // Use the centralized FlowOutcomeProcessor for consistent processing

                if (ConvSessFlow.outcomes != null && ConvSessFlow.outcomes.Any())
                {
                    try
                    {
                        var flowProcessor = new FlowOutcomeProcessor(_logger, AppTimeZone);
                        var flowOutcomeResult = await flowProcessor.ProcessFlowOutcomesAsync(
                            new[] { ConvSessFlow },
                            Conv.conversationId,
                            Conv.conversationStart,
                            Conv.conversationEnd,
                            FlowOutcomes,
                            throwOnInvalidDates: false); // Use false to match the original graceful error handling

                        // Add "F" to status string to indicate flow outcome processing
                        if (flowOutcomeResult.TotalProcessed > 0)
                        {
                            // Flow outcomes processed
                        }

                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Error processing flow outcomes for conversation {ConversationId}: {ErrorMessage}",
                            Conv.conversationId, ex.Message);
                        // Continue processing other conversations
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing flow data for conversation {ConversationId}", Conv.conversationId);
                throw;
            }
        }

        /// <summary>
        /// Processes flow data and outcomes
        /// </summary>
        private async Task ProcessFlowData(Interactions.Conversation Conv, Interactions.Flow ConvSessFlow, DataRow NewRow,
            DataTable FlowOutcomes, TimeZoneInfo AppTimeZone, object lockObject)
        {
            try
            {
                NewRow["flowid"] = ConvSessFlow.flowId;
                NewRow["flowname"] = ConvSessFlow.flowName;

                try
                {
                    NewRow["flowversion"] = decimal.Round(decimal.Parse(ConvSessFlow.flowVersion), 2);
                }
                catch
                {
                    NewRow["flowversion"] = 1.0;
                }

                NewRow["flowtype"] = ConvSessFlow.flowType;
                NewRow["exitreason"] = ConvSessFlow.exitReason;
                NewRow["entryreason"] = ConvSessFlow.entryReason;
                NewRow["entrytype"] = ConvSessFlow.entryType;
                NewRow["transfertype"] = ConvSessFlow.transferType;
                if (ConvSessFlow.transferTargetName != null && ConvSessFlow.transferTargetName.Length > 254)
                    NewRow["transfertargetname"] = ConvSessFlow.transferTargetName.Substring(0, 254);
                else
                    NewRow["transfertargetname"] = ConvSessFlow.transferTargetName;

                NewRow["issuedcallback"] = ConvertBoolean(ConvSessFlow.issuedCallback);

                // Use the centralized FlowOutcomeProcessor for consistent processing
                if (ConvSessFlow.outcomes != null && ConvSessFlow.outcomes.Any())
                {
                    try
                    {
                        var flowProcessor = new FlowOutcomeProcessor(_logger, AppTimeZone);
                        var flowOutcomeResult = await flowProcessor.ProcessFlowOutcomesAsync(
                            new[] { ConvSessFlow },
                            Conv.conversationId,
                            Conv.conversationStart,
                            Conv.conversationEnd,
                            FlowOutcomes,
                            throwOnInvalidDates: false); // Use false to match the original graceful error handling

                        // Add "F" to status string to indicate flow outcome processing
                        if (flowOutcomeResult.TotalProcessed > 0)
                        {
                            // Flow outcomes processed
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "Error processing flow outcomes for conversation {ConversationId}: {ErrorMessage}",
                            Conv.conversationId, ex.Message);
                        // Continue processing other conversations
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing flow data for conversation {ConversationId}", Conv.conversationId);
                throw;
            }
        }

        /// <summary>
        /// Processes session metrics (thread-safe version)
        /// </summary>
        private async Task ProcessMetricsThreadSafe(Interactions.Metric[] metrics, Dictionary<string, object> rowData, DataRow DRPartSumm, Interactions.Conversation Conv = null)
        {
            try
            {
                foreach (Interactions.Metric ConvSessMetric in metrics)
                {
                    string FirstChar = ConvSessMetric.name.Substring(0, 1);
                    try
                    {
                        switch (FirstChar)
                        {
                            case "n":
                                if (ConvSessMetric.value > 0)
                                {
                                    // Store in row data - column existence will be checked when creating the actual row
                                    rowData[ConvSessMetric.name] = ConvSessMetric.value;

                                    // Check if column exists in participant summary table before setting value
                                    if (DRPartSumm.Table.Columns.Contains(ConvSessMetric.name))
                                    {
                                        DRPartSumm[ConvSessMetric.name] = ConvSessMetric.value;
                                    }
                                    else
                                    {
                                        _logger?.LogDebug("Column '{MetricName}' does not exist in participantsummaryData table. Skipping metric.", ConvSessMetric.name);
                                    }
                                }
                                break;
                            case "t":
                                if (ConvSessMetric.value > 0)
                                {
                                    if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                        ConvSessMetric.value += 100;

                                    decimal calculatedValue;
                                    if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                    {
                                        calculatedValue = (decimal)Math.Round(ConvSessMetric.value / 1000.00F, 2) + 0.11m;
                                    }
                                    else
                                    {
                                        calculatedValue = (decimal)Math.Round(ConvSessMetric.value / 1000.00F, 2);
                                    }

                                    // Store in row data - column existence will be checked when creating the actual row
                                    rowData[ConvSessMetric.name] = calculatedValue;

                                    // Check if column exists in participant summary table before setting value
                                    if (DRPartSumm.Table.Columns.Contains(ConvSessMetric.name))
                                    {
                                        DRPartSumm[ConvSessMetric.name] = calculatedValue;
                                    }
                                    // Column doesn't exist in participant summary table - skip silently
                                }
                                break;
                        }
                    }
                    catch (Exception e)
                    {
                        _logger?.LogWarning(e, "Error processing metric '{MetricName}' with value {MetricValue}. Error: {ErrorMessage}",
                            ConvSessMetric.name, ConvSessMetric.value, e.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing metrics for conversation {ConversationId}", Conv?.conversationId ?? "Unknown");
                throw;
            }
        }

        /// <summary>
        /// Processes session metrics
        /// </summary>
        private async Task ProcessMetrics(Interactions.Metric[] metrics, DataRow NewRow, DataRow DRPartSumm, Interactions.Conversation Conv = null)
        {
            try
            {
                foreach (Interactions.Metric ConvSessMetric in metrics)
                {
                    string FirstChar = ConvSessMetric.name.Substring(0, 1);
                    try
                    {
                        switch (FirstChar)
                        {
                            case "n":
                                if (ConvSessMetric.value > 0)
                                {
                                    // Check if column exists before setting value
                                    if (NewRow.Table.Columns.Contains(ConvSessMetric.name))
                                    {
                                        NewRow[ConvSessMetric.name] = ConvSessMetric.value;
                                    }
                                    else
                                    {
                                        _logger?.LogDebug("Column '{MetricName}' does not exist in detailedInteractionData table. Skipping metric.", ConvSessMetric.name);
                                    }

                                    // Check if column exists in participant summary table before setting value
                                    if (DRPartSumm.Table.Columns.Contains(ConvSessMetric.name))
                                    {
                                        DRPartSumm[ConvSessMetric.name] = ConvSessMetric.value;
                                    }
                                    else
                                    {
                                        _logger?.LogDebug("Column '{MetricName}' does not exist in participantsummaryData table. Skipping metric.", ConvSessMetric.name);
                                    }
                                }
                                break;
                            case "t":
                                if (ConvSessMetric.value > 0)
                                {
                                    if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                        ConvSessMetric.value += 100;

                                    decimal calculatedValue;
                                    if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                    {
                                        calculatedValue = (decimal)Math.Round(ConvSessMetric.value / 1000.00F, 2) + 0.11m;
                                    }
                                    else
                                    {
                                        calculatedValue = (decimal)Math.Round(ConvSessMetric.value / 1000.00F, 2);
                                    }

                                    // Check if column exists before setting value
                                    if (NewRow.Table.Columns.Contains(ConvSessMetric.name))
                                    {
                                        NewRow[ConvSessMetric.name] = calculatedValue;
                                    }
                                    else
                                    {
                                        _logger?.LogDebug("Column '{MetricName}' does not exist in detailedInteractionData table. Skipping metric.", ConvSessMetric.name);
                                    }

                                    // Check if column exists in participant summary table before setting value
                                    if (DRPartSumm.Table.Columns.Contains(ConvSessMetric.name))
                                    {
                                        DRPartSumm[ConvSessMetric.name] = calculatedValue;
                                    }
                                    else
                                    {
                                        _logger?.LogDebug("Column '{MetricName}' does not exist in participantsummaryData table. Skipping metric.", ConvSessMetric.name);
                                    }
                                }
                                break;
                        }
                    }
                    catch (Exception e)
                    {
                        _logger?.LogWarning(e, "Error processing metric '{MetricName}' with value {MetricValue}. Error: {ErrorMessage}",
                            ConvSessMetric.name, ConvSessMetric.value, e.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing metrics for conversation {ConversationId}", Conv?.conversationId ?? "Unknown");
                throw;
            }
        }

        /// <summary>
        /// Processes the JSON data from a single page of conversation details query for FlowOutcome data only
        /// </summary>
        private async Task ProcessFlowOutcomePageData(string JsonString, DataTable FlowOutcomes, object lockObject, int pageNumber, TimeZoneInfo appTimeZone)
        {
            try
            {
                // This method contains the existing JSON processing logic that was in the while loop
                // Moving the existing processing logic here to enable concurrent processing

                Interactions.InteractionSegmentStruct DetailData = JsonConvert.DeserializeObject<Interactions.InteractionSegmentStruct>(JsonString,
                               new JsonSerializerSettings
                               {
                                   NullValueHandling = NullValueHandling.Ignore
                               });

                if (DetailData?.conversations == null)
                {
                    _logger?.LogWarning("FlowOutcome: Page {PageNumber} contains no conversations", pageNumber);
                    return;
                }

                int conversationsProcessed = 0;
                foreach (Interactions.Conversation Conv in DetailData.conversations)
                {
                    foreach (Interactions.Participant ConvPart in Conv.participants)
                    {
                        foreach (Interactions.Session ConvSess in ConvPart.sessions)
                        {
                            Interactions.Flow ConvSessFlow = ConvSess.flow;
                            int SegmentCount = ConvSess.segments.Length;
                            int CurrentSegment = 1;

                            foreach (Interactions.Segment ConvSeg in ConvSess.segments)
                            {
                                if (CurrentSegment == SegmentCount && ConvSessFlow != null && ConvSessFlow.flowId != null)
                                {
                                    // Use the centralized FlowOutcomeProcessor for consistent processing
                                    if (ConvSessFlow.outcomes != null && ConvSessFlow.outcomes.Any())
                                    {
                                        try
                                        {
                                            var flowProcessor = new FlowOutcomeProcessor(_logger, appTimeZone);
                                            var flowOutcomeResult = await flowProcessor.ProcessFlowOutcomesAsync(
                                                new[] { ConvSessFlow },
                                                Conv.conversationId,
                                                Conv.conversationStart,
                                                Conv.conversationEnd,
                                                FlowOutcomes,
                                                throwOnInvalidDates: false); // Use false to match the original graceful error handling

                                            // Thread-safe status update
                                            if (flowOutcomeResult.TotalProcessed > 0)
                                            {
                                                lock (lockObject)
                                                {
                                                    // Flow outcomes processed
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger?.LogError(ex, "FlowOutcome: Error processing flow outcomes for conversation {ConversationId} on page {PageNumber}: {ErrorMessage}",
                                                Conv.conversationId, pageNumber, ex.Message);
                                            // Continue processing other conversations
                                        }
                                    }
                                }
                                CurrentSegment++;
                            }
                        }
                    }
                    conversationsProcessed++;
                }

                _logger?.LogDebug("FlowOutcome: Page {PageNumber} processed {ConversationCount} conversations", pageNumber, conversationsProcessed);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "FlowOutcome: Error processing JSON data for page {PageNumber}", pageNumber);
                throw;
            }
        }

        /// <summary>
        /// Checks and enforces rate limiting for participant attributes processing
        /// Uses configurable rate limits with defaults of 65% of 3000 requests per 60 seconds (1950 requests per minute)
        /// </summary>
        private async Task CheckParticipantAttributesRateLimit()
        {
            lock (_rateLimitLock)
            {
                DateTime now = DateTime.UtcNow;
                TimeSpan elapsed = now - _participantAttributesRateLimitWindowStart;

                // Reset counter if more than the configured window duration has passed
                if (elapsed.TotalSeconds >= _windowDurationSeconds)
                {
                    _participantAttributesRequestCount = 0;
                    _participantAttributesRateLimitWindowStart = now;
                }

                // Check if we've hit the configured rate limit
                if (_participantAttributesRequestCount >= _maxRequestsPerMinute)
                {
                    double remainingSeconds = _windowDurationSeconds - elapsed.TotalSeconds;
                    if (remainingSeconds > 0)
                    {
                        _logger?.LogWarning("ParticipantAttributes:RateLimit: Hit rate limit ({MaxRequests}/min). Processed {TotalProcessed} conversations. Pausing {RemainingSeconds:F1}s.",
                            _maxRequestsPerMinute, _totalUniqueConversationsProcessed, remainingSeconds);

                        // Wait for the remainder of the 60-second window
                        Thread.Sleep(TimeSpan.FromSeconds(remainingSeconds + 1)); // Add 1 second buffer

                        // Reset the counter and window
                        _participantAttributesRequestCount = 0;
                        _participantAttributesRateLimitWindowStart = DateTime.UtcNow;
                        _logger?.LogInformation("ParticipantAttributes:RateLimit: Pause complete. Processed {TotalProcessed} conversations. Resuming.",
                            _totalUniqueConversationsProcessed);
                    }
                }

                // Increment the request counter (no verbose logging)
                _participantAttributesRequestCount++;
            }
        }

        /// <summary>
        /// Gets a fresh API token for each batch to ensure optimal performance and avoid token-related rate limits
        /// </summary>
        private async Task<string> GetFreshTokenForBatch(string originalApiKey, int batchNumber)
        {
            try
            {
                // Log token refresh attempt for batch
                // _logger?.LogDebug("ParticipantAttributes:Token: Getting fresh token for batch {BatchNumber}", batchNumber);

                // Use the centralized GCUtils to get a fresh token
                var gcUtils = new GCUtils(_logger);
                bool success = gcUtils.GetGCAPIKey();

                if (success && !string.IsNullOrEmpty(gcUtils.GCApiKey))
                {
                    // _logger?.LogDebug("ParticipantAttributes:Token: Successfully obtained fresh token for batch {BatchNumber}", batchNumber);
                    return gcUtils.GCApiKey;
                }
                else
                {
                    _logger?.LogWarning("ParticipantAttributes:Token: Failed to get fresh token for batch {BatchNumber}, using original token", batchNumber);
                    return originalApiKey;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "ParticipantAttributes:Token: Exception getting fresh token for batch {BatchNumber}, using original token", batchNumber);
                return originalApiKey;
            }
        }

        private async Task ProcessConversationBatch(List<DataRow> conversationBatch, DataTable ParticipantAttributes, string URI, string GCApiKey, int MaxNVarCharLength, object lockObject)
        {
            int batchProcessedCount = 0;
            int failedCount = 0;
            int duplicateCount = 0;
            int rateLimitFailures = 0;

            foreach (var SmallConvRow in conversationBatch)
            {
                try
                {
                    // Add null check for SmallConvRow and conversationid
                    if (SmallConvRow == null || SmallConvRow["conversationid"] == null || SmallConvRow["conversationid"] == System.DBNull.Value)
                    {
                        _logger?.LogWarning("SmallConvRow or conversationid is null. Skipping this conversation.");
                        continue;
                    }

                    string currentConversationId = SmallConvRow["conversationid"].ToString();
                    if (string.IsNullOrEmpty(currentConversationId))
                    {
                        _logger?.LogWarning("conversationid is empty. Skipping this conversation.");
                        failedCount++;
                        continue;
                    }

                    // Check if we already have a row for this conversation to avoid duplicates
                    bool conversationExists = false;
                    lock (lockObject)
                    {
                        conversationExists = ParticipantAttributes.AsEnumerable()
                            .Any(row => row.Field<string>("conversationid") == currentConversationId);
                    }

                    if (conversationExists)
                    {
                        // Suppress individual duplicate logs to reduce noise - tracked in batch summary
                        duplicateCount++;
                        continue;
                    }

                    string media = "calls"; // Default to calls
                    if (SmallConvRow["mediatype"] != null && SmallConvRow["mediatype"] != System.DBNull.Value)
                    {
                        string mediaType = SmallConvRow["mediatype"].ToString();
                        if (!string.IsNullOrEmpty(mediaType))
                        {
                            switch (mediaType)
                            {
                                case "voice":
                                    media = "calls";
                                    break;
                                default:
                                    media = mediaType + "s";
                                    break;
                            }
                        }
                    }
                    else
                    {
                        // Suppress individual mediatype warnings to reduce noise - tracked in batch summary
                        failedCount++;
                    }

                    // Check rate limit before making API call
                    await CheckParticipantAttributesRateLimit();

                    // Use JsonReturnString instead of JsonReturnStringGetAsync to get built-in retry logic
                    // JsonReturnString has comprehensive rate limiting handling with proper retry mechanisms
                    string JsonString = JsonActions.JsonReturnString(URI + "/api/v2/conversations/" + media + "/" + currentConversationId, GCApiKey);

                    // Enhanced validation to detect rate limiting and other API failures
                    bool isValidResponse = !string.IsNullOrEmpty(JsonString) && JsonString.Length > 10;
                    bool isErrorResponse = JsonString != null && (JsonString.Contains("\"error\": true") || JsonString.Contains("\"statusCode\": \"TooManyRequests\""));

                    if (isValidResponse && !isErrorResponse)
                    {
                        PartAttribs.ParticipantAttributes DetailData = JsonConvert.DeserializeObject<PartAttribs.ParticipantAttributes>(JsonString,
                                          new JsonSerializerSettings
                                          {
                                              NullValueHandling = NullValueHandling.Ignore
                                          });

                        if (DetailData != null)
                        {
                            DataRow DRPartAttrib = null;
                            string capturedConversationId = null;

                            try
                            {
                                // Capture conversation ID BEFORE any DataTable operations to prevent race conditions
                                if (SmallConvRow["conversationid"] != null && SmallConvRow["conversationid"] != System.DBNull.Value)
                                {
                                    capturedConversationId = SmallConvRow["conversationid"].ToString();
                                }
                                else
                                {
                                    // Suppress individual null conversationid warnings to reduce noise - tracked in batch summary
                                    failedCount++;
                                    continue;
                                }

                                // Entire row creation and population must be thread-safe
                                // The issue is that multiple threads are accessing the same DataTable schema simultaneously
                                lock (lockObject)
                                {
                                    DRPartAttrib = ParticipantAttributes.NewRow();

                                    // Assign both fields immediately after row creation to prevent race conditions
                                    DRPartAttrib["keyid"] = capturedConversationId;
                                    DRPartAttrib["conversationid"] = capturedConversationId;

                                    // CRITICAL VALIDATION: Immediately verify keyid and conversationid match
                                    string assignedKeyId = DRPartAttrib["keyid"]?.ToString() ?? "";
                                    string assignedConversationId = DRPartAttrib["conversationid"]?.ToString() ?? "";

                                    if (assignedKeyId != assignedConversationId)
                                    {
                                        _logger?.LogError("ParticipantAttributes:Validation: CRITICAL - keyid '{KeyId}' does not match conversationid '{ConversationId}' during row creation for conversation {OriginalId}",
                                            assignedKeyId, assignedConversationId, capturedConversationId);

                                        // Log additional debugging information about the threading issue
                                        _logger?.LogError("ParticipantAttributes:Validation: Threading Debug - Current conversation: {CurrentConv}, Captured: {CapturedConv}, KeyId: {KeyId}, ConvId: {ConvId}",
                                            currentConversationId, capturedConversationId, assignedKeyId, assignedConversationId);

                                        throw new InvalidOperationException($"CRITICAL DATA CORRUPTION: keyid '{assignedKeyId}' does not match conversationid '{assignedConversationId}' during row creation. " +
                                            $"Current conversation: '{currentConversationId}', Captured: '{capturedConversationId}'. This indicates a threading race condition in DataRow creation.");
                                    }
                                }
                                // Populate additional conversation fields
                                if (SmallConvRow["conversationstartdate"] != null && SmallConvRow["conversationstartdate"] != System.DBNull.Value)
                                    DRPartAttrib["conversationstartdate"] = SmallConvRow["conversationstartdate"];

                                if (SmallConvRow["conversationstartdateltc"] != null && SmallConvRow["conversationstartdateltc"] != System.DBNull.Value)
                                    DRPartAttrib["conversationstartdateltc"] = SmallConvRow["conversationstartdateltc"];

                                if (SmallConvRow["conversationenddate"] != null && SmallConvRow["conversationenddate"] != System.DBNull.Value)
                                {
                                    if (Convert.ToDateTime(SmallConvRow["conversationenddate"]) > DateTime.UtcNow.AddYears(-20))
                                    {
                                        DRPartAttrib["conversationenddate"] = SmallConvRow["conversationenddate"];
                                        if (SmallConvRow["conversationenddateltc"] != null && SmallConvRow["conversationenddateltc"] != System.DBNull.Value)
                                            DRPartAttrib["conversationenddateltc"] = SmallConvRow["conversationenddateltc"];
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                // Suppress individual DataRow creation errors to reduce noise - tracked in batch summary
                                failedCount++;
                                continue;
                            }

                            // Add null check for participants collection
                            if (DetailData.participants != null)
                            {
                                foreach (PartAttribs.Participant ConvPart in DetailData.participants)
                                {
                                    // Add null check for participant
                                    if (ConvPart == null)
                                    {
                                        // Suppress individual null participant warnings to reduce noise - tracked in batch summary
                                        continue;
                                    }

                                    if (ConvPart.attributes != null)
                                    {
                                        Dictionary<string, string> values = null;
                                        try
                                        {
                                            string attributesJson = ConvPart.attributes?.ToString();
                                            if (!string.IsNullOrWhiteSpace(attributesJson))
                                            {
                                                values = JsonConvert.DeserializeObject<Dictionary<string, string>>(attributesJson);
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            // Suppress individual deserialization warnings to reduce noise - tracked in batch summary
                                            continue; // Skip this participant's attributes and move to the next
                                        }

                                        if (values == null)
                                        {
                                            // Suppress individual "no attributes" debug logs to reduce noise
                                            continue; // Skip this participant's attributes and move to the next
                                        }

                                        // Thread-safe column addition
                                        lock (lockObject)
                                        {
                                            DataColumnCollection columns = ParticipantAttributes.Columns;

                                            foreach (KeyValuePair<string, string> kvp in values)
                                            {
                                                if (!string.IsNullOrEmpty(kvp.Key))
                                                {
                                                    string ConParamName = RenameParticipantAttributeNames(_renameParticipantAttributeNames, kvp.Key);

                                                    // Add null check for ConParamName
                                                    if (ConParamName == null)
                                                    {
                                                        // Suppress individual rename warnings to reduce noise - tracked in batch summary
                                                        continue;
                                                    }

                                                    if (ConParamName.Length <= 55)
                                                        ConParamName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());
                                                    else if (ConParamName.Length >= 55)
                                                        ConParamName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());

                                                    if (DBUtil.DBType == CSG.Adapter.Configuration.DatabaseType.PostgreSQL && ConParamName != null)
                                                        ConParamName = ConParamName.ToLower();

                                                    // Add null check before using ConParamName
                                                    if (!string.IsNullOrEmpty(ConParamName) && !columns.Contains(ConParamName))
                                                    {
                                                        ParticipantAttributes.Columns.Add(ConParamName, typeof(String));
                                                        // Suppress individual column addition console output to reduce noise
                                                    }
                                                }
                                            }

                                            ParticipantAttributes.AcceptChanges();
                                        }

                                        foreach (KeyValuePair<string, string> kvp in values)
                                        {
                                            string ConParamName = String.Empty;
                                            string AttribValue = String.Empty;
                                            string AttribName = String.Empty;

                                            if (!string.IsNullOrEmpty(kvp.Key))
                                            {
                                                ConParamName = RenameParticipantAttributeNames(_renameParticipantAttributeNames, kvp.Key);
                                                if (ConParamName == null)
                                                {
                                                    // Suppress individual rename warnings to reduce noise - tracked in batch summary
                                                    continue;
                                                }

                                                if (ConParamName.Length <= 55)
                                                    ConParamName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());
                                                else if (ConParamName.Length >= 55)
                                                    ConParamName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());

                                                if (DBUtil.DBType == CSG.Adapter.Configuration.DatabaseType.PostgreSQL)
                                                    ConParamName = ConParamName.ToLower();

                                                switch (DBUtil.DBType)
                                                {
                                                    case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                                                    case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                                                    case CSG.Adapter.Configuration.DatabaseType.MySQL:
                                                        AttribName = ConParamName;
                                                        break;
                                                    case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                                                        if (!string.IsNullOrEmpty(ConParamName))
                                                        {
                                                            if (ConParamName.Length <= 55)
                                                                AttribName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray()).ToLower();
                                                            else
                                                                AttribName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray()).ToLower();
                                                        }
                                                        break;
                                                    default:
                                                        throw new NotImplementedException("Database type is not implemented");
                                                }

                                                // Add null check for AttribName before using it
                                                if (!string.IsNullOrEmpty(AttribName))
                                                {
                                                    AttribValue = kvp.Value ?? "";
                                                    if (AttribValue.Length > MaxNVarCharLength)
                                                        AttribValue = AttribValue.Substring(0, MaxNVarCharLength - 1);

                                                    try
                                                    {
                                                        DRPartAttrib[AttribName] = AttribValue;
                                                    }
                                                    catch (ArgumentException ex)
                                                    {
                                                        // Suppress individual attribute assignment warnings to reduce noise - tracked in batch summary
                                                    }
                                                }
                                                else
                                                {
                                                    // Suppress individual AttribName warnings to reduce noise - tracked in batch summary
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else
                            {
                                // Suppress individual "no participants" debug logs to reduce noise
                            }

                            // Always add a row for every conversation, even if it has no participant attributes
                            // This ensures complete data coverage and prevents conversations from being "missing" from the table

                            // Add the row with proper error handling (thread-safe)
                            if (DRPartAttrib != null)
                            {
                                // Track unique conversations using unified tracking
                                string conversationId = currentConversationId;
                                bool isNewConversation = _processedConversationIds.TryAdd(conversationId, true);

                                lock (lockObject)
                                {
                                    try
                                    {
                                        ParticipantAttributes.Rows.Add(DRPartAttrib);
                                        batchProcessedCount++;

                                        // Only increment global counter for truly new conversations
                                        if (isNewConversation)
                                        {
                                            Interlocked.Increment(ref _totalUniqueConversationsProcessed);
                                        }
                                        // Participant attribute added - using debug level to avoid log flooding
                                    }
                                    catch (Exception ex)
                                    {
                                        // Suppress individual row addition errors to reduce noise - tracked in batch summary
                                        failedCount++;
                                    }
                                }
                            }
                            else
                            {
                                // Suppress individual null DataRow warnings to reduce noise - tracked in batch summary
                                failedCount++;
                            }
                        }
                    }
                    else
                    {
                        // API call failed or returned insufficient data - enhanced error analysis
                        string failureReason = "API call failed or returned insufficient data";
                        bool isRateLimitFailure = false;

                        if (string.IsNullOrEmpty(JsonString))
                        {
                            failureReason = $"Empty response (HTTP {JsonActions.responseCode ?? "Unknown"})";
                        }
                        else if (JsonString.Length <= 10)
                        {
                            failureReason = $"Short response ({JsonString.Length} chars)";
                        }
                        else if (isErrorResponse)
                        {
                            if (JsonString.Contains("\"statusCode\": \"TooManyRequests\"") || JsonString.Contains("Rate limiting exceeded"))
                            {
                                failureReason = "Rate limiting exceeded retry limit";
                                isRateLimitFailure = true;
                            }
                            else if (JsonString.Contains("\"statusCode\": \"Forbidden\""))
                            {
                                failureReason = "Access forbidden";
                            }
                            else if (JsonString.Contains("\"statusCode\": \"NotFound\""))
                            {
                                failureReason = "Resource not found";
                            }
                            else
                            {
                                failureReason = $"API error response: {JsonString.Substring(0, Math.Min(100, JsonString.Length))}";
                            }
                        }

                        // Use appropriate log level based on failure type
                        if (isRateLimitFailure)
                        {
                            _logger?.LogError("RATE LIMITING FAILURE for conversation {ConversationId} (media: {Media}). {FailureReason}. This conversation will be missing from participant attributes data.",
                                currentConversationId, media, failureReason);
                            rateLimitFailures++;
                        }
                        else
                        {
                            _logger?.LogWarning("API call failed for conversation {ConversationId} (media: {Media}). {FailureReason}. Skipping participant attributes processing for this conversation.",
                                currentConversationId, media, failureReason);
                        }

                        failedCount++;
                    }
                }
                catch (Exception ex)
                {
                    // Log the error and continue processing other conversations
                    string currentConversationId = "unknown";
                    try
                    {
                        if (SmallConvRow?["conversationid"] != null && SmallConvRow["conversationid"] != System.DBNull.Value)
                        {
                            currentConversationId = SmallConvRow["conversationid"].ToString();
                        }
                    }
                    catch
                    {
                        // Ignore errors getting conversation ID for logging
                    }

                    _logger?.LogError(ex, "Unexpected error processing conversation {ConversationId}. Skipping participant attributes processing for this conversation. Exception: {ExceptionMessage}",
                        currentConversationId, ex.Message);

                    failedCount++;
                    // Continue processing other conversations instead of failing the entire job
                    continue;
                }
            }

            // Batch processing summary with rate limiting visibility
            int totalProcessed = batchProcessedCount + failedCount + duplicateCount;
            if (totalProcessed != conversationBatch.Count)
            {
                _logger?.LogError("CRITICAL: Conversation count mismatch in batch! Expected: {Expected}, Processed: {Processed}, Failed: {Failed}, Duplicates: {Duplicates}, Total Accounted: {TotalAccounted}",
                    conversationBatch.Count, batchProcessedCount, failedCount, duplicateCount, totalProcessed);
            }

            // Enhanced batch summary logging with meaningful prefixes
            if (rateLimitFailures > 0)
            {
                _logger?.LogError("ParticipantAttributes:Batch: Processed {ProcessedCount} successful, {FailedCount} failed ({RateLimitFailures} rate limited), {DuplicateCount} duplicates, {TotalCount} total",
                    batchProcessedCount, failedCount, rateLimitFailures, duplicateCount, conversationBatch.Count);
            }
            else if (failedCount > 0)
            {
                _logger?.LogWarning("ParticipantAttributes:Batch: Processed {ProcessedCount} successful, {FailedCount} failed, {DuplicateCount} duplicates, {TotalCount} total",
                    batchProcessedCount, failedCount, duplicateCount, conversationBatch.Count);
            }
            else
            {
                _logger?.LogInformation("ParticipantAttributes:Batch: Processed {ProcessedCount} successful, {FailedCount} failed, {DuplicateCount} duplicates, {TotalCount} total",
                    batchProcessedCount, failedCount, duplicateCount, conversationBatch.Count);
            }
        }

        public DataTable CreateSmallConversationDetails()
        {
            DataTable DTTemp = new DataTable();

            DTTemp.Columns.Add("conversationid", typeof(String));
            DTTemp.Columns.Add("conversationstartdate", typeof(DateTime));
            DTTemp.Columns.Add("conversationenddate", typeof(DateTime));
            DTTemp.Columns.Add("conversationstartdateltc", typeof(DateTime));
            DTTemp.Columns.Add("conversationenddateltc", typeof(DateTime));
            DTTemp.Columns.Add("conversationminmos", typeof(decimal));
            DTTemp.Columns.Add("conversationminrfactor", typeof(decimal));
            DTTemp.Columns.Add("originaldirection", typeof(String));
            DTTemp.Columns.Add("mediatype", typeof(String));
            DTTemp.Columns.Add("ani", typeof(String));
            DTTemp.Columns.Add("dnis", typeof(String));
            DTTemp.Columns.Add("remoteName", typeof(String));

            DTTemp.TableName = "simpleInteractionData";

            foreach (DataColumn DTTempCol in DTTemp.Columns)
            {
                DTTempCol.AllowDBNull = true;
                DTTempCol.DefaultValue = System.DBNull.Value;
            }

            return DTTemp;
        }

        private int ConvertBoolean(bool Inbound)
        {
            return Inbound ? 1 : 0;
        }

        private int DetermineMaxNVarCharLength()
        {
            switch (DBUtil.DBType)
            {
                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                    return 200;
                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                    return 50;
                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                    return 100;
                default:
                    throw new NotImplementedException("Database type is not implemented");
            }
        }

        public async Task<DataSet> GetDetailInteractionDataFromGCJob(String StartDate, String EndDate)
        {
            int MaxNVarCharLength = DetermineMaxNVarCharLength();
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);

            _logger?.LogInformation("Using timezone: {TimeZone}", TimeZoneConfig);
            _logger?.LogInformation("Data retrieval window: {StartDate} to {EndDate}", StartDate, EndDate);

            DataSet DSDetailInteraction = new DataSet();
            _logger?.LogDebug("Initializing detailed interaction data table");
            DataTable DetailInteraction = DBUtil.CreateInMemTable("detailedInteractionData");
            bool timeFieldsMillisecondResolution = DetailInteraction.Columns["segmenttime"].DataType == typeof(System.Decimal);

            _logger?.LogDebug("Initializing data tables for job mode processing");
            DataTable ParticipantAttributes = DBUtil.CreateInMemTable("participantAttributesDynamic");
            DataTable ParticipantSummary = DBUtil.CreateInMemTable("participantsummaryData");
            DataTable FlowOutcomes = DBUtil.CreateInMemTable("flowoutcomedata");

            _logger?.LogInformation("Retrieving detailed interaction data starting from: {StartDate}", StartDate);

            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString() ?? "";

            string RequestBody = "{" +
                                 " \"interval\": \"" + StartDate + "/" + EndDate + "\", " +
                                 " \"order\": \"asc\"," +
                                 " \"orderBy\": \"conversationEnd\"," +
                                 " \"flattenMultivaluedDimensions\": true," +
                                 " \"dimensions\": [\"conversationId\", \"participantId\", \"sessionId\", \"flowId\", \"flowName\", \"flowType\", \"flowVersion\", \"flowOutcomeId\", \"flowOutcomeName\", \"flowOutcomeValue\"]" +
                                 "}";

            _logger?.LogDebug("API request body: {RequestBody}", RequestBody);
            var apiResponse = JsonActions.JsonReturnHttpResponse(URI + "/api/v2/analytics/conversations/details/jobs", GCApiKey, RequestBody);

            // Validate response using proper HTTP status code detection
            if (string.IsNullOrWhiteSpace(apiResponse.Content))
            {
                _logger?.LogError("Empty response received from API - aborting data retrieval");
                return DSDetailInteraction;
            }

            // Handle different HTTP status codes appropriately
            if (!apiResponse.IsSuccess && !apiResponse.IsAccepted)
            {
                _logger?.LogError("API Error in GetDetailInteractionDataFromGCJob: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                    apiResponse.StatusCode, apiResponse.StatusDescription, apiResponse.Content);

                // Check for specific error types that should halt processing
                if (apiResponse.StatusCode == 400 || apiResponse.StatusCode == 403)
                {
                    _logger?.LogError("Critical API error detected (HTTP {StatusCode}) - halting processing", apiResponse.StatusCode);
                    throw new HttpRequestException($"API returned HTTP {apiResponse.StatusCode}: {apiResponse.Content}");
                }

                // For other errors, return empty dataset to indicate failure
                _logger?.LogError("API returned error response - aborting data retrieval");
                return DSDetailInteraction;
            }

            DetInt.ReportJob? JobID = null;
            try
            {
                JobID = JsonConvert.DeserializeObject<DetInt.ReportJob>(apiResponse.Content,
                                   new JsonSerializerSettings
                                   {
                                       NullValueHandling = NullValueHandling.Ignore
                                   });
            }
            catch (JsonException jsonEx)
            {
                // Include problematic JSON in the error message for debugging
                string jsonPreview = apiResponse.Content.Length > 200 ? apiResponse.Content.Substring(0, 200) + "..." : apiResponse.Content;
                _logger?.LogError(jsonEx, "JSON Deserialization Error in GetDetailInteractionDataFromGCJob. HTTP Status: {StatusCode}. Problematic JSON: {JsonPreview}",
                    apiResponse.StatusCode, jsonPreview);
                throw;
            }

            if (JobID == null)
            {
                _logger?.LogError("Failed to parse job ID from API response");
                return DSDetailInteraction;
            }

            // Wait for job completion using polling
            _logger?.LogInformation("Waiting for job {JobId} completion via polling", JobID.jobId);
            var jobCompletionResult = await WaitForJobCompletionViaPollingAsync(URI, JobID.jobId);

            if (!jobCompletionResult.Success)
            {
                _logger?.LogError("Interactions: Job {JobId} failed - {ErrorMessage}", JobID.jobId, jobCompletionResult.ErrorMessage);

                // If the error is related to a critical failure, we should throw an exception
                // to make sure the job doesn't exit with a success code
                if (jobCompletionResult.ErrorMessage.Contains("Timeout waiting for job") ||
                    jobCompletionResult.ErrorMessage.Contains("Error waiting for job completion"))
                {
                    _logger?.LogError("Failed to poll job {JobId} status: {ErrorMessage}", JobID.jobId, jobCompletionResult.ErrorMessage);
                    throw new InvalidOperationException($"Failed to poll job {JobID.jobId} status: {jobCompletionResult.ErrorMessage}");
                }

                return DSDetailInteraction;
            }

            _logger?.LogInformation("Job {JobId} completed successfully via polling", JobID.jobId);

            _logger?.LogInformation("Interactions: Job ID {JobId} Status: FULFILLED", JobID.jobId);

            string LastCursor = String.Empty;
            int cursorLoopCount=0;
            List<DetInt.DetailedInteractions> detailedInteractionsList = new List<DetInt.DetailedInteractions>();

            var detailedInteractions = await FetchDataAsync(URI, GCApiKey, JobID.jobId, LastCursor);
            while (detailedInteractions != null && detailedInteractions.conversations != null && detailedInteractions.conversations.Count() > 0)
            {
                _logger?.LogInformation("Retrieving data page {Page} with cursor: {Cursor}", cursorLoopCount, detailedInteractions.cursor);
                detailedInteractionsList.Add(detailedInteractions);
                if (string.IsNullOrEmpty(detailedInteractions.cursor))
                    break;
                LastCursor = detailedInteractions.cursor;
                detailedInteractions = await FetchDataAsync(URI, GCApiKey, JobID.jobId, LastCursor);
                cursorLoopCount++;
            }

            // Flatten all conversations into one list
            var allConversations = detailedInteractionsList
                .Where(d => d.conversations != null && d.conversations.Count() > 0)
                .SelectMany(d => d.conversations)
                .ToList();

            int batchSize = 50000;
            if (allConversations.Count > 0)
            {
                var batches = allConversations
                    .Select((Conv, index) => new { Conv, index })
                    .GroupBy(x => x.index / batchSize)
                    .Select(g => g.Select(x => x.Conv).ToList())
                    .ToList();
                _logger?.LogInformation("Processing data in {BatchCount} batches", batches.Count);

                bool result = await ProcessDataAsync(batches, DetailInteraction, ParticipantSummary, ParticipantAttributes, FlowOutcomes, AppTimeZone, timeFieldsMillisecondResolution, MaxNVarCharLength);
                _logger?.LogInformation("All data batches processed successfully");
            }
            else
            {
                _logger?.LogInformation("No conversation data returned from API");
            }

            DSDetailInteraction.Tables.Add(DetailInteraction);
            DSDetailInteraction.Tables.Add(ParticipantAttributes);
            DSDetailInteraction.Tables.Add(ParticipantSummary);
            DSDetailInteraction.Tables.Add(FlowOutcomes);



            _logger?.LogInformation("Latest conversation date found: {LatestDate}", DetailInteractionLastUpdate);

            return DSDetailInteraction;
        }

        private async Task<DetInt.DetailedInteractions> FetchDataAsync(string uri, string apiKey, string jobID, string LastCursor)
        {
            try
            {
                string CursorString = string.IsNullOrEmpty(LastCursor) ? "" : "?cursor=" + HttpUtility.UrlEncode(LastCursor);
                string url = $"{uri}/api/v2/analytics/conversations/details/jobs/{jobID}/results{CursorString}";

                var apiResponse = await JsonActions.JsonReturnHttpResponseGetAsync(url, apiKey);

                // Validate response using proper HTTP status code detection
                if (string.IsNullOrWhiteSpace(apiResponse.Content))
                {
                    _logger?.LogWarning("Empty response received in FetchDataAsync - returning null");
                    return null;
                }

                // Handle different HTTP status codes appropriately
                if (!apiResponse.IsSuccess)
                {
                    _logger?.LogError("API Error in FetchDataAsync: HTTP {StatusCode} - {StatusDescription}. Response: {Response}",
                        apiResponse.StatusCode, apiResponse.StatusDescription, apiResponse.Content);

                    // Check for specific error types that should halt processing
                    if (apiResponse.StatusCode == 400 || apiResponse.StatusCode == 403)
                    {
                        _logger?.LogError("Critical API error detected in FetchDataAsync (HTTP {StatusCode}) - halting processing", apiResponse.StatusCode);
                        throw new HttpRequestException($"API returned HTTP {apiResponse.StatusCode} in FetchDataAsync: {apiResponse.Content}");
                    }

                    // For other errors, return null to indicate failure
                    _logger?.LogWarning("Returning null from FetchDataAsync due to HTTP {StatusCode} error", apiResponse.StatusCode);
                    return null;
                }

                DetInt.DetailedInteractions detailData = null;
                try
                {
                    detailData = JsonConvert.DeserializeObject<DetInt.DetailedInteractions>(apiResponse.Content,
                        new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
                }
                catch (JsonException jsonEx)
                {
                    // Include problematic JSON in the error message for debugging
                    string jsonPreview = apiResponse.Content.Length > 200 ? apiResponse.Content.Substring(0, 200) + "..." : apiResponse.Content;
                    _logger?.LogError(jsonEx, "JSON Deserialization Error in FetchDataAsync. HTTP Status: {StatusCode}. Problematic JSON: {JsonPreview}",
                        apiResponse.StatusCode, jsonPreview);
                    throw;
                }

                return detailData;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error retrieving data from API in FetchDataAsync: {Message}", ex.Message);
                // Error already logged by _logger?.LogError above
                return null;
            }
        }

        private async Task<bool> ProcessDataAsync(
            IEnumerable<IEnumerable<DetInt.Conversation>> batches,
            DataTable DetailInteraction,
            DataTable ParticipantSummary,
            DataTable ParticipantAttributes,
            DataTable FlowOutcomes,
            TimeZoneInfo AppTimeZone,
            bool timeFieldsMillisecondResolution,
            int MaxNVarCharLength)
        {
            var taskExceptions = new ConcurrentBag<Exception>();

            var detailInteractionRows = new ConcurrentBag<DataRow>();
            var participantSummaryRows = new ConcurrentBag<DataRow>();
            var participantAttributeRows = new ConcurrentBag<DataRow>();
            var flowOutcomesRows = new ConcurrentBag<DataRow>();

            int processedCount = 0;
            int constraintViolationCount = 0;
            Stopwatch totalStopwatch = Stopwatch.StartNew();
            Stopwatch segmentStopwatch = Stopwatch.StartNew();

            // We will print a status message every time we process another 1000 records
            int printInterval = 100;

            var tasks = batches.Select(batch =>
                Task.Run(async () =>
                {
                    try
                    {
                        foreach (var Conv in batch)
                        {
                            // Check if we already have a row for this conversation to avoid duplicates
                            // This is critical because both ProcessDataAsync and ProcessParticipantAttributesAsync
                            // can be called in the same execution, leading to duplicate key constraint violations
                            bool conversationExists = participantAttributeRows.Any(row =>
                                row.Field<string>("conversationid") == Conv.conversationId) ||
                                ParticipantAttributes.AsEnumerable().Any(row =>
                                row.Field<string>("conversationid") == Conv.conversationId);

                            if (conversationExists)
                            {
                                _logger?.LogDebug("Conversation {ConversationId} already exists in ParticipantAttributes. Skipping to avoid duplicates in ProcessDataAsync.",
                                    Conv.conversationId);
                                continue;
                            }

                            DataRow DRPartAttrib = ParticipantAttributes.NewRow();
                            // Capture conversation ID once to ensure consistency (defensive programming)
                            string conversationId = Conv.conversationId;
                            DRPartAttrib["keyid"] = conversationId;
                            DRPartAttrib["conversationid"] = conversationId;
                            // Conv.conversationStart is already UTC from API deserialization
                            // Validate conversation start date - throw if invalid to maintain data integrity
                            if (Conv.conversationStart == default(DateTime))
                            {
                                throw new InvalidDataException($"Invalid conversation start date {Conv.conversationStart} for conversation {Conv.conversationId}. Cannot process conversation with invalid date.");
                            }

                            DRPartAttrib["conversationstartdate"] = Conv.conversationStart;
                            DRPartAttrib["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart, AppTimeZone);

                            if (Conv.conversationEnd > DateTime.UtcNow.AddYears(-20))
                            {
                                // Conv.conversationEnd is already UTC from API deserialization
                                DRPartAttrib["conversationenddate"] = Conv.conversationEnd;
                                DRPartAttrib["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd, AppTimeZone);
                            }

                            int PartCode = 0;
                            if (Conv.participants != null)
                            {
                                foreach (var ConvPart in Conv.participants)
                                {
                                    // Add null check for participant
                                    if (ConvPart == null)
                                    {
                                        _logger?.LogWarning("Null participant found in conversation {ConversationId}. Skipping participant.", Conv.conversationId);
                                        continue;
                                    }

                                    // Add null check for critical participant properties
                                    if (string.IsNullOrEmpty(ConvPart.participantId))
                                    {
                                        _logger?.LogWarning("Participant with null or empty participantId found in conversation {ConversationId}. Skipping participant.", Conv.conversationId);
                                        continue;
                                    }

                                    DataRow DRPartSumm = ParticipantSummary.NewRow();
                                    DRPartSumm["keyid"] = Conv.conversationId + "|" + ConvPart.participantId;
                                    DRPartSumm["conversationid"] = Conv.conversationId;
                                    DRPartSumm["participantid"] = ConvPart.participantId;
                                    // Handle null purpose value to prevent NoNullAllowedException
                                    DRPartSumm["purpose"] = !string.IsNullOrEmpty(ConvPart.purpose) ? ConvPart.purpose : "unknown";

                                    if (Conv.divisionIds != null && Conv.divisionIds.Length > 0 && !string.IsNullOrEmpty(Conv.divisionIds[0]))
                                    {
                                        DRPartSumm["divisionid"] = Conv.divisionIds[0];
                                    }
                                    else
                                    {
                                        DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                    }

                                    if (DRPartSumm["divisionid"] == null || DRPartSumm["divisionid"] == System.DBNull.Value|| DRPartSumm["divisionid"] is DBNull)
                                    {
                                        _logger?.LogDebug("Setting default division ID for participant summary in async processing");
                                        DRPartSumm["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                    }
                                    if (Conv.divisionIds != null && Conv.divisionIds.Length > 1 && !string.IsNullOrEmpty(Conv.divisionIds[1]))
                                        DRPartSumm["divisionid2"] = Conv.divisionIds[1];
                                    if (Conv.divisionIds != null && Conv.divisionIds.Length > 2 && !string.IsNullOrEmpty(Conv.divisionIds[2]))
                                        DRPartSumm["divisionid3"] = Conv.divisionIds[2];

                                    // Conv.conversationStart is already UTC from API deserialization
                                    // Validate conversation start date - throw if invalid to maintain data integrity
                                    if (Conv.conversationStart == default(DateTime))
                                    {
                                        throw new InvalidDataException($"Invalid conversation start date {Conv.conversationStart} for conversation {Conv.conversationId}. Cannot process conversation with invalid date.");
                                    }

                                    DRPartSumm["conversationstartdate"] = Conv.conversationStart;
                                    DRPartSumm["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart, AppTimeZone);

                                    if (Conv.conversationEnd > DateTime.UtcNow.AddYears(-20))
                                    {
                                        // Conv.conversationEnd is already UTC from API deserialization
                                        DRPartSumm["conversationenddate"] = Conv.conversationEnd;
                                        DRPartSumm["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd, AppTimeZone);
                                    }
                                    else
                                    {
                                        DRPartSumm["conversationenddate"] = System.DBNull.Value;
                                        DRPartSumm["conversationenddateltc"] = System.DBNull.Value;
                                    }

                                    PartCode++;
                                    int SessCode = 0;
                                    int SegCode = 0;

                                    // Handle participant attributes safely
                                    if (ConvPart.attributes != null)
                                    {
                                        Dictionary<string, string> values = null;
                                        try
                                        {
                                            string attributesJson = ConvPart.attributes?.ToString();
                                            if (!string.IsNullOrWhiteSpace(attributesJson))
                                            {
                                                values = JsonConvert.DeserializeObject<Dictionary<string, string>>(attributesJson);
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger?.LogWarning(ex, "Failed to deserialize participant attributes for conversation {ConversationId} in async processing. Skipping attributes processing.",
                                                Conv.conversationId);
                                            continue; // Skip this participant and move to the next
                                        }

                                        if (values == null)
                                        {
                                            _logger?.LogDebug("No valid participant attributes found for conversation {ConversationId} in async processing",
                                                Conv.conversationId);
                                            continue; // Skip this participant and move to the next
                                        }

                                        DataColumnCollection columns = ParticipantAttributes.Columns;

                                        // Add columns if needed
                                        foreach (KeyValuePair<string, string> kvp in values)
                                        {
                                            if (!string.IsNullOrEmpty(kvp.Key))
                                            {
                                                string ConParamName = RenameParticipantAttributeNames(_renameParticipantAttributeNames, kvp.Key);

                                                if (ConParamName != null && ConParamName.Length <= 55)
                                                    ConParamName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());
                                                else if (kvp.Value != null && ConParamName.ToString().Length >= 55)
                                                    ConParamName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());

                                                if (DBUtil.DBType == CSG.Adapter.Configuration.DatabaseType.PostgreSQL && ConParamName != null)
                                                    ConParamName = ConParamName.ToLower();

                                                if (!columns.Contains(ConParamName))
                                                {
                                                    lock (_participantAttributeLock)
                                                    {
                                                        if (!columns.Contains(ConParamName))
                                                            ParticipantAttributes.Columns.Add(ConParamName, typeof(String));
                                                    }
                                                }
                                            }
                                        }

                                        // Assign attribute values
                                        foreach (KeyValuePair<string, string> kvp in values)
                                        {
                                            string ConParamName = String.Empty;
                                            string AttribValue = String.Empty;
                                            string AttribName = String.Empty;

                                            if (!string.IsNullOrEmpty(kvp.Key))
                                            {
                                                ConParamName = RenameParticipantAttributeNames(_renameParticipantAttributeNames, kvp.Key);
                                                if (ConParamName == null) continue;

                                                if (ConParamName != null && ConParamName.Length <= 55)
                                                    ConParamName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());
                                                else if (kvp.Value != null && ConParamName.ToString().Length >= 55)
                                                    ConParamName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray());

                                                if (DBUtil.DBType == CSG.Adapter.Configuration.DatabaseType.PostgreSQL)
                                                    ConParamName = ConParamName.ToLower();

                                                switch (DBUtil.DBType)
                                                {
                                                    case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                                                    case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                                                    case CSG.Adapter.Configuration.DatabaseType.MySQL:
                                                        AttribName = ConParamName;
                                                        break;
                                                    case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                                                        if (ConParamName != null && ConParamName.Length <= 55)
                                                            AttribName = new string(ConParamName.Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray()).ToLower();
                                                        else if (kvp.Value != null && ConParamName.Length >= 55)
                                                            AttribName = new string(ConParamName.Substring(0, 54).Where(c => char.IsLetter(c) || char.IsDigit(c) || char.IsPunctuation(c)).ToArray()).ToLower();
                                                        break;
                                                default:
                                                    throw new NotImplementedException("Database type is not implemented");
                                                }

                                                AttribValue = kvp.Value ?? "";
                                                if (AttribValue.Length > MaxNVarCharLength)
                                                    AttribValue = AttribValue.Substring(0, MaxNVarCharLength - 1);

                                                DRPartAttrib[AttribName] = AttribValue;
                                            }
                                        }
                                    }

                                    if (ConvPart.sessions != null)
                                    {
                                        foreach (DetInt.Session ConvSess in ConvPart.sessions)
                                        {
                                            SessCode++;
                                            SegCode = 0;
                                            //Console.WriteLine("Currently Looking at Session  :{0}", ConvSess.sessionId);

                                            DetInt.Flow ConvSessFlow = ConvSess.flow;


                                            int SegmentCount = ConvSess.segments.Length;
                                            int CurrentSegment = 1;

                                            foreach (DetInt.Segment ConvSeg in ConvSess.segments)
                                            {
                                                SegCode++;
                                                //Console.WriteLine("Currently Looking at Segment  :{0}", ConvSeg.segmentType);

                                                string IterationCode = PartCode.ToString() + "|" + SessCode.ToString() + "|" + SegCode.ToString();

                                                DataRow NewRow = DetailInteraction.NewRow();
                                                string TempKeyid = Conv.conversationId + "|" + IterationCode;
                                                string rowKey = Conv.conversationId + "|ID:" + UCAUtils.GetStableHashCode(TempKeyid);

                                                NewRow["keyid"] = rowKey;
                                                //NewRow["keyid"] = Conv.conversationId;
                                                NewRow["conversationid"] = Conv.conversationId;


                                                if (Conv.divisionIds[0] != null && Conv.divisionIds[0] != "")
                                                {
                                                    NewRow["divisionid"] = Conv.divisionIds[0];
                                                }
                                                else
                                                {
                                                    NewRow["divisionid"] = "00000000-0000-0000-0000-0000000000000";
                                                }

                                                if (NewRow["divisionid"] == null
                                                    || NewRow["divisionid"] is DBNull
                                                    || (NewRow["divisionid"] is string str && string.IsNullOrWhiteSpace(str)))
                                                {
                                                    _logger?.LogDebug("Setting default division ID for detailed interaction in async processing");
                                                    NewRow["divisionid"] = "00000000-0000-0000-0000-000000000000";
                                                }

                                                if (Conv.divisionIds.Count() > 1 && Conv.divisionIds[1] != null && Conv.divisionIds[1] != "")
                                                {
                                                    NewRow["divisionid2"] = Conv.divisionIds[1];
                                                }
                                                else
                                                {
                                                    NewRow["divisionid2"] = "";
                                                }
                                                if (Conv.divisionIds.Count() > 2 && Conv.divisionIds[2] != null && Conv.divisionIds[2] != "")
                                                {
                                                    NewRow["divisionid3"] = Conv.divisionIds[2];
                                                }
                                                else
                                                {
                                                    NewRow["divisionid3"] = "";
                                                }
                                                // Conv.conversationStart is already UTC from API deserialization
                                                // Validate conversation start date - throw if invalid to maintain data integrity
                                                if (Conv.conversationStart == default(DateTime))
                                                {
                                                    throw new InvalidDataException($"Invalid conversation start date {Conv.conversationStart} for conversation {Conv.conversationId}. Cannot process conversation with invalid date.");
                                                }

                                                NewRow["conversationstartdate"] = Conv.conversationStart;
                                                NewRow["conversationstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationStart, AppTimeZone);

                                                if (Conv.conversationEnd != null && Conv.conversationEnd > DateTime.UtcNow.AddYears(-20))
                                                {
                                                    // Conv.conversationEnd is already UTC from API deserialization
                                                    NewRow["conversationenddate"] = Conv.conversationEnd;
                                                    NewRow["conversationenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(Conv.conversationEnd, AppTimeZone);
                                                }

                                                NewRow["gencode"] = IterationCode;

                                                DateTime MaxDateTest = Conv.conversationStart;
                                                if (MaxDateTest > DetailInteractionLastUpdate)
                                                {
                                                    DetailInteractionLastUpdate = MaxDateTest;
                                                    // Console.Write("@");
                                                }

                                                NewRow["conversationminmos"] = decimal.Round(Conv.mediaStatsMinConversationMos, 2);
                                                NewRow["originaldirection"] = Conv.originatingDirection;
                                                NewRow["participantid"] = ConvPart.participantId;
                                                NewRow["peer"] = ConvSess.peerId;
                                                if (ConvPart.participantName != null && ConvPart.participantName.Length > 250)
                                                    NewRow["participantname"] = ConvPart.participantName.Substring(0, 250);
                                                else
                                                    NewRow["participantname"] = ConvPart.participantName;
                                                // Handle null purpose value to prevent NoNullAllowedException
                                                NewRow["purpose"] = !string.IsNullOrEmpty(ConvPart.purpose) ? ConvPart.purpose : "unknown";
                                                NewRow["mediatype"] = ConvSess.mediaType;

                                                DRPartSumm["mediaType"] = ConvSess.mediaType;

                                                if (ConvSess.ani != null && ConvSess.ani.Length > 300
                                                                        && (ConvSess.ani.IndexOf("tel:") > 0 || ConvSess.ani.IndexOf("sip:") > 0))
                                                    NewRow["ani"] = ConvSess.ani.Substring(0, 300);
                                                else
                                                    NewRow["ani"] = ConvSess.ani;

                                                if (ConvSeg.queueId != null)
                                                {
                                                    NewRow["queueid"] = ConvSeg.queueId;
                                                    DRPartSumm["queueid"] = ConvSeg.queueId;
                                                }
                                                if (ConvPart.userId != null)
                                                {
                                                    NewRow["userid"] = ConvPart.userId;
                                                    DRPartSumm["userid"] = ConvPart.userId;
                                                }



                                                if (ConvSess.dnis != null && ConvSess.dnis.Length > 300)
                                                    NewRow["dnis"] = ConvSess.dnis.Substring(0, 300);
                                                else
                                                    NewRow["dnis"] = ConvSess.dnis;


                                                if (ConvSess.sessionDnis != null && ConvSess.sessionDnis.Length > 300)
                                                    NewRow["sessiondnis"] = ConvSess.sessionDnis.Substring(0, 300);
                                                else
                                                    NewRow["sessiondnis"] = ConvSess.sessionDnis;


                                                NewRow["sessiondirection"] = ConvSess.direction;
                                                NewRow["edgeId"] = ConvSess.edgeId;
                                                if (ConvSess.remote != null && ConvSess.remote.Length > 250)
                                                    NewRow["remotedisplayable"] = ConvSess.remote.Substring(0, 249);
                                                else
                                                    NewRow["remotedisplayable"] = ConvSess.remote;

                                                NewRow["conversationminrfactor"] = decimal.Round(Conv.mediaStatsMinConversationRFactor, 2);
                                                if (Conv.externalTag != null)
                                                    NewRow["externalTag"] = Conv.externalTag;

                                                if (! timeFieldsMillisecondResolution)
                                                {
                                                    ConvSeg.segmentStart = new DateTime(
                                                        ConvSeg.segmentStart.Ticks - (ConvSeg.segmentStart.Ticks % TimeSpan.TicksPerSecond),
                                                        ConvSeg.segmentStart.Kind
                                                    );

                                                    ConvSeg.segmentEnd = new DateTime(
                                                        ConvSeg.segmentEnd.Ticks - (ConvSeg.segmentEnd.Ticks % TimeSpan.TicksPerSecond),
                                                        ConvSeg.segmentEnd.Kind
                                                    );
                                                }
                                                // ConvSeg.segmentStart is already UTC from API deserialization
                                                var segmentStartUtc = ConvSeg.segmentStart;
                                                NewRow["segmentstartdate"] = segmentStartUtc;
                                                NewRow["segmentstartdateltc"] = TimeZoneInfo.ConvertTimeFromUtc(segmentStartUtc, AppTimeZone);


                                                System.TimeSpan Diff = new System.TimeSpan();

                                                if (ConvSeg.segmentEnd > DateTime.UtcNow.AddYears(-20))
                                                {
                                                    // ConvSeg.segmentEnd is already UTC from API deserialization
                                                    var segmentEndUtc = ConvSeg.segmentEnd;
                                                    NewRow["segmentenddate"] = segmentEndUtc;
                                                    NewRow["segmentenddateltc"] = TimeZoneInfo.ConvertTimeFromUtc(segmentEndUtc, AppTimeZone);
                                                    Diff = ConvSeg.segmentEnd - ConvSeg.segmentStart;
                                                    NewRow["segmenttime"] = Diff.TotalSeconds;
                                                    Diff = ConvSeg.segmentEnd - Conv.conversationStart;
                                                    NewRow["convtosegmentendtime"] = Diff.TotalSeconds;
                                                }
                                                else
                                                {
                                                    NewRow["segmentenddate"] = System.DBNull.Value;
                                                    NewRow["segmenttime"] = System.DBNull.Value;
                                                    NewRow["convtosegmentendtime"] = System.DBNull.Value;
                                                }

                                                Diff = ConvSeg.segmentStart - Conv.conversationStart;
                                                NewRow["convtosegmentstarttime"] = Diff.TotalSeconds;


                                                NewRow["segmenttype"] = ConvSeg.segmentType;
                                                NewRow["conference"] = ConvertBoolean(ConvSeg.conference);
                                                NewRow["segdestinationConversationId"] = ConvSeg.destinationConversationId;


                                                string RowWrapUp = ConvSeg.wrapUpCode;
                                                string RowWrapUpNote = ConvSeg.wrapUpNote;

                                                if (RowWrapUp != null)
                                                {
                                                    if (RowWrapUp == "ININ-WRAP-UP-TIMEOUT")
                                                        RowWrapUp = "00000000-0000-0000-0000-0000000000000";
                                                    NewRow["wrapupcode"] = RowWrapUp;
                                                    DRPartSumm["wrapupcode"] = RowWrapUp;
                                                    if (RowWrapUpNote != null)
                                                    {
                                                        NewRow["wrapupnote"]= ConvSeg.wrapUpNote;
                                                        DRPartSumm["wrapupnote"] =ConvSeg.wrapUpNote;
                                                    }
                                                    else
                                                    {
                                                        NewRow["wrapupnote"]= "";
                                                    }

                                                }
                                                else
                                                {
                                                    NewRow["wrapupcode"] = "";
                                                    NewRow["wrapupnote"]= "";
                                                }

                                                if (ConvSeg.disconnectType == null)
                                                    NewRow["disconnectiontype"] = "none";
                                                else
                                                    NewRow["disconnectiontype"] = ConvSeg.disconnectType;

                                                NewRow["recordingexists"] = ConvertBoolean(ConvSess.recording);
                                                NewRow["sessionprovider"] = ConvSess.provider;


                                                if (CurrentSegment == SegmentCount)
                                                {
                                                    //Insert Flow Data into last Segment
                                                    if (ConvSessFlow != null)
                                                    {
                                                        if (ConvSessFlow.flowId != null)
                                                        {
                                                            NewRow["flowid"] = ConvSessFlow.flowId;
                                                            if (ConvSessFlow.flowName != null && ConvSessFlow.flowName.Length > 254)
                                                                NewRow["flowname"] = ConvSessFlow.flowName.Substring(0, 254);
                                                            else
                                                                NewRow["flowname"] = ConvSessFlow.flowName;
                                                            try
                                                            {
                                                                NewRow["flowversion"] = decimal.Round(decimal.Parse(ConvSessFlow.flowVersion), 2);
                                                            }
                                                            catch
                                                            {
                                                                NewRow["flowversion"] = 1.0;
                                                            }
                                                            NewRow.SetFieldValue(rowKey, "flowtype", ConvSessFlow.flowType);
                                                            NewRow.SetFieldValue(rowKey, "exitreason", ConvSessFlow.exitReason);
                                                            NewRow.SetFieldValue(rowKey, "entryreason", ConvSessFlow.entryReason);
                                                            NewRow.SetFieldValue(rowKey, "entrytype", ConvSessFlow.entryType);
                                                            NewRow.SetFieldValue(rowKey, "transfertype", ConvSessFlow.transferType);
                                                            if (ConvSessFlow.transferTargetName != null && ConvSessFlow.transferTargetName.Length > 254)
                                                                NewRow["transfertargetname"] = ConvSessFlow.transferTargetName.Substring(0, 254);
                                                            else
                                                                NewRow["transfertargetname"] = ConvSessFlow.transferTargetName;

                                                            NewRow["issuedcallback"] = ConvertBoolean(ConvSessFlow.issuedCallback);

                                                            // Use the centralized FlowOutcomeProcessor for consistent processing

                                                            if (ConvSessFlow.outcomes != null && ConvSessFlow.outcomes.Any())
                                                            {
                                                                try
                                                                {
                                                                    var flowProcessor = new FlowOutcomeProcessor(_logger, AppTimeZone);

                                                                    // Create a temporary DataTable to collect flow outcomes for this conversation
                                                                    var tempFlowOutcomes = FlowOutcomes.Clone();

                                                                    var flowOutcomeResult = await flowProcessor.ProcessFlowOutcomesAsync(
                                                                        new[] { ConvSessFlow },
                                                                        Conv.conversationId,
                                                                        Conv.conversationStart,
                                                                        Conv.conversationEnd,
                                                                        tempFlowOutcomes,
                                                                        throwOnInvalidDates: true); // Use true to match the original strict error handling

                                                                    // Add the processed rows to the thread-safe collection
                                                                    foreach (DataRow row in tempFlowOutcomes.Rows)
                                                                    {
                                                                        flowOutcomesRows.Add(row);
                                                                    }



                                                                    // Add "F" to status string to indicate flow outcome processing
                                                                    // Note: In async processing, we don't use Console.Write due to thread safety concerns
                                                                    // The flow outcome processing is tracked through the overall progress indicators
                                                                    if (flowOutcomeResult.TotalProcessed > 0)
                                                                    {
                                                                        // Flow outcomes processed - status tracked via overall progress metrics
                                                                    }
                                                                }
                                                                catch (Exception ex)
                                                                {
                                                                    _logger?.LogError(ex, "Error processing flow outcomes for conversation {ConversationId}: {ErrorMessage}",
                                                                        Conv.conversationId, ex.Message);
                                                                    // Re-throw to maintain existing behavior for this implementation
                                                                    throw;
                                                                }
                                                            }
                                                        }
                                                    }

                                                    if (ConvSess.metrics != null && ConvSess.metrics.Length > 0)
                                                    {

                                                        foreach (DetInt.Metric ConvSessMetric in ConvSess.metrics)
                                                        {
                                                            string FirstChar = ConvSessMetric.name.Substring(0, 1);

                                                            try
                                                            {
                                                                // Check if the column exists in both tables before attempting assignment
                                                                bool detailColumnExists = DetailInteraction.Columns.Contains(ConvSessMetric.name);
                                                                bool summaryColumnExists = ParticipantSummary.Columns.Contains(ConvSessMetric.name);

                                                                if (!detailColumnExists && !summaryColumnExists)
                                                                {
                                                                    // Suppress individual debug messages for missing metric columns to reduce log noise
                                                                    continue;
                                                                }

                                                                switch (FirstChar)
                                                                {
                                                                    case "n":
                                                                        if (ConvSessMetric.value > 0)
                                                                        {
                                                                            if (detailColumnExists)
                                                                                NewRow[ConvSessMetric.name] = ConvSessMetric.value;
                                                                            if (summaryColumnExists)
                                                                                DRPartSumm[ConvSessMetric.name] = ConvSessMetric.value;
                                                                        }
                                                                        break;
                                                                    case "t":
                                                                        if (ConvSessMetric.value > 0)
                                                                        {
                                                                            if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                                                                ConvSessMetric.value = ConvSessMetric.value + 100;

                                                                            if (Math.Round(ConvSessMetric.value / 1000.00F, 2) == Convert.ToInt32(Math.Round(ConvSessMetric.value / 1000.00F, 2)))
                                                                            {
                                                                                var adjustedValue = Math.Round(ConvSessMetric.value / 1000.00F, 2) + 0.11;
                                                                                if (detailColumnExists)
                                                                                    NewRow[ConvSessMetric.name] = adjustedValue;
                                                                                if (summaryColumnExists)
                                                                                    DRPartSumm[ConvSessMetric.name] = adjustedValue;
                                                                            }
                                                                            else
                                                                            {
                                                                                var roundedValue = Math.Round(ConvSessMetric.value / 1000.00F, 2);
                                                                                if (detailColumnExists)
                                                                                    NewRow[ConvSessMetric.name] = roundedValue;
                                                                                if (summaryColumnExists)
                                                                                    DRPartSumm[ConvSessMetric.name] = roundedValue;
                                                                            }
                                                                        }
                                                                        break;
                                                                }
                                                            }
                                                            catch (Exception ex)
                                                            {
                                                                _logger?.LogWarning(ex, "Failed to assign metric '{MetricName}' with value {MetricValue}", ConvSessMetric.name, ConvSessMetric.value);
                                                            }

                                                        }
                                                    }

                                                    //if (ConvSess.metrics != null || ConvSessFlow != null)
                                                    //    Console.Write("%");
                                                }
                                                detailInteractionRows.Add(NewRow);

                                                CurrentSegment++;
                                                // Console.Write("#");
                                            }
                                        }
                                    }

                                    participantSummaryRows.Add(DRPartSumm);
                                }
                            }

                            // Add participant attributes row with proper error handling
                            try
                            {
                                participantAttributeRows.Add(DRPartAttrib);
                            }
                            catch (System.Data.ConstraintException ex)
                            {
                                // Suppress individual constraint violation debug messages to reduce log noise
                                Interlocked.Increment(ref constraintViolationCount);
                                // Continue processing - this is expected when the same conversation is processed multiple times
                            }

                            // Track unique conversations using unified tracking
                            bool isNewConversation = _processedConversationIds.TryAdd(Conv.conversationId, true);

                            // Increment the processed counter for every conversation (local counter for progress reporting)
                            int localCount = Interlocked.Increment(ref processedCount);

                            // Only increment global counter for truly new conversations
                            if (isNewConversation)
                            {
                                Interlocked.Increment(ref _totalUniqueConversationsProcessed);
                            }

                            // Every 1000 records, print a status
                            if (localCount % printInterval == 0)
                            {
                                var elapsed = segmentStopwatch.Elapsed;
                                _logger?.LogInformation("Processing progress: {ProcessedCount} records processed in {ElapsedSeconds:F2} seconds",
                                    localCount, elapsed.TotalSeconds);
                                segmentStopwatch.Restart(); // reset the segment stopwatch
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        taskExceptions.Add(ex);
                    }
                })
            ).ToList();

            try
            {
                await Task.WhenAll(tasks);
            }
            catch (Exception ex)
            {
                taskExceptions.Add(ex);
            }

            if (taskExceptions.Count > 0)
            {
                _logger?.LogError("Error: {ExceptionCount} exceptions occurred during data processing", taskExceptions.Count);
                foreach (var ex in taskExceptions)
                {
                    _logger?.LogError(ex, "Data processing exception");
                }
                return false;
            }

            totalStopwatch.Stop();

            _logger?.LogDebug("Merging processed data into database tables");

            lock (_detailInteractionLock)
            {
                foreach (var row in detailInteractionRows)
                {
                    try
                    {
                        DataRow CheckRowExists = DetailInteraction.Select("keyid= '" + row["keyid"] + "'").FirstOrDefault();
                        if (CheckRowExists == null)
                            DetailInteraction.Rows.Add(row);
                    }
                    catch (Exception ex)
                    {
                        string conversationId = row["conversationid"]?.ToString() ?? row["keyid"]?.ToString() ?? "Unknown";
                        _logger?.LogError(ex, "Error adding row to detailed interaction table for conversation {ConversationId}", conversationId);
                    }
                }
            }

            lock (_participantSummaryLock)
            {
                foreach (var row in participantSummaryRows)
                {
                    try
                    {
                        DataRow CheckPartSumm = ParticipantSummary.Select("keyid= '" + row["keyid"] + "'").FirstOrDefault();
                        if (CheckPartSumm == null)
                            ParticipantSummary.Rows.Add(row);
                    }
                    catch (Exception ex)
                    {
                        string conversationId = row["conversationid"]?.ToString() ?? row["keyid"]?.ToString()?.Split('|')[0] ?? "Unknown";
                        _logger?.LogError(ex, "Error adding row to participant summary table for conversation {ConversationId}", conversationId);
                    }
                }
            }

            lock (_participantAttributeLock)
            {
                foreach (var row in participantAttributeRows)
                {
                    try
                    {
                        DataRow CheckPartAttrib = ParticipantAttributes.Select("keyid= '" + row["keyid"] + "'").FirstOrDefault();
                        if (CheckPartAttrib == null)
                            ParticipantAttributes.Rows.Add(row);
                    }
                    catch (Exception ex)
                    {
                        string conversationId = row["conversationid"]?.ToString() ?? row["keyid"]?.ToString() ?? "Unknown";
                        _logger?.LogError(ex, "Error adding row to participant attributes table for conversation {ConversationId}", conversationId);
                    }
                }
            }

            lock (_flowOutcomesLock)
            {
                foreach (var row in flowOutcomesRows)
                {
                    try
                    {
                        DataRow CheckFlowOutcome = FlowOutcomes.Select("keyid= '" + row["keyid"] + "'").FirstOrDefault();
                        if (CheckFlowOutcome == null)
                            FlowOutcomes.Rows.Add(row);
                    }
                    catch (Exception ex)
                    {
                        string conversationId = row["conversationid"]?.ToString() ?? row["keyid"]?.ToString() ?? "Unknown";
                        _logger?.LogError(ex, "Error adding row to flow outcomes table for conversation {ConversationId}", conversationId);
                    }
                }
            }

            // Log flow outcome processing summary
            if (flowOutcomesRows.Count > 0)
            {
                _logger?.LogInformation("Flow outcome processing completed: {FlowOutcomeCount} flow outcomes processed", flowOutcomesRows.Count);
            }
            else
            {
                _logger?.LogInformation("Flow outcome processing completed: No flow outcomes found in API response data");
            }

            // Debug: Log final FlowOutcomes table count
            _logger?.LogDebug("Final FlowOutcomes table count: {FlowOutcomeTableCount} rows", FlowOutcomes.Rows.Count);



            _logger?.LogDebug("ProcessConversationDetailsPageData completed");

            // Summary logging for constraint violations (replaces repetitive individual debug messages)
            if (constraintViolationCount > 0)
            {
                _logger?.LogInformation("ProcessDataAsync constraint violation summary: {ConstraintViolations} duplicate participant attributes constraint violations suppressed to reduce log noise", constraintViolationCount);
            }

            return true;
        }

    }

    public class JobDateLimit
    {
        public DateTime dataAvailabilityDate { get; set; }
    }
}
