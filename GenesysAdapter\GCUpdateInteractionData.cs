using System;
using System.Linq;
using System.Data;
using System.Globalization;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;
using GCData;
using StandardUtils;
using GenesysCloudUtils;
using CSG.Common.ExtensionMethods;
using CSG.Adapter.Configuration;
using CSG.Adapter.Licensing;
using DBUtils;

namespace GenesysAdapter
{
    #nullable enable
    class GCUpdateInteractionData
    {
        private const int MAX_DIFF_DAYS = 45; // max difference days before default range
        private const decimal DECIMAL_TOLERANCE = 0; // old logic used exact rounding
        private const int DIFFING_THRESHOLD = 100000; // Only perform diffing if there are more than 100k rows

        // Voice data (shared DataTables)
        private DataSet OverAllVoiceStats = new DataSet();
        private DataTable VoiceOverView = new DataTable();
        private DataTable VoiceTopics = new DataTable();
        private DataTable VoiceSentiment = new DataTable();

        private readonly Utils UCAUtils = new Utils();
        private readonly ILogger? _logger;
        private readonly BackfillUtils BackfillUtils = new BackfillUtils();
        // Flag to track if Knowledge Quest is enabled via license
        private bool _enableKnowledgeQuest = false;

        // Progress tracking and summary statistics (thread-safe counters)
        private static int _totalConversationsProcessed = 0;
        private static int _totalOverviewRowsAdded = 0;
        private static int _totalTopicRowsAdded = 0;
        private static int _totalSentimentRowsAdded = 0;
        private static int _totalTranscriptsProcessed = 0;
        private static int _totalTranscriptsFailed = 0;
        private static int _totalQueuesVerified = 0;
        private static int _totalQueuesSkipped = 0;
        private static readonly object _progressLock = new object();
        private static DateTime _lastProgressLogTime = DateTime.MinValue;
        private const int PROGRESS_LOG_INTERVAL = 50; // Log progress every 50 conversations

        // Participant Attributes batch processing counters (thread-safe)
        private static int _totalConvSummaryProcessed = 0;
        private static int _totalConvSummaryWritten = 0;
        private static int _totalConvSummaryErrors = 0;
        private static int _totalParticipantAttributesProcessed = 0;
        private static int _totalParticipantAttributesWritten = 0;
        private static int _totalParticipantAttributesSkipped = 0;
        private static int _totalParticipantAttributesErrors = 0;
        private static int _totalParticipantSummaryProcessed = 0;
        private static int _totalParticipantSummaryWritten = 0;
        private static int _totalParticipantSummaryErrors = 0;
        private static int _totalFlowOutcomeProcessed = 0;
        private static int _totalFlowOutcomeWritten = 0;
        private static int _totalFlowOutcomeErrors = 0;
        private static readonly object _participantProgressLock = new object();
        private static DateTime _lastParticipantProgressLogTime = DateTime.MinValue;
        private const int PARTICIPANT_PROGRESS_LOG_INTERVAL = 10000; // Log progress every 10k rows
        private static readonly TimeSpan MIN_LOG_INTERVAL = TimeSpan.FromSeconds(10); // Minimum time between progress logs

        // Flag to control Knowledge Quest feature

        public GCUpdateInteractionData(ILogger logger)
        {
            _logger = logger;
        }

        #region User/Queue Interaction

        public bool UpdateGCUserInteractionData(bool Backfill = false)
        {
            bool Successful = false;
            string CurrentJob = "userinteractiondata";
            string BackfillJob = CurrentJob + "_backfill";
            string SyncType = Backfill ? BackfillJob : CurrentJob;
            DateTime Start = DateTime.Now;

            _logger?.LogInformation("Job:Start: Beginning {SyncType} job", SyncType);

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            if (Backfill)
            {
                Successful = BackfillUtils.ConfigureBackfill(CurrentJob, SyncType, GCData, DBAdapter, _logger);

                if (!Successful){
                    return Successful;
                }
            }

            // Get user data from Genesys
            // Pass granularity parameter (null for default)
            DataTable UserInteractionData = GCData.UserInteractionData();
            _logger?.LogInformation("Job:Data: Retrieved {Count} rows from Genesys Cloud for user interaction", UserInteractionData.Rows.Count);

            // Diff the data against what's already in the database
            DateTime startDate = GCData.DateToSyncFrom.Subtract(GCData.LookBackSpan);
            DateTime endDate = GCData.DateToSyncFrom.Add(GCData.MaxSpanToSync);

            // Create the where condition for the diffing query
            string whereCondition = $@"
                startdate >= '{startDate:yyyy-MM-dd HH:mm:ss}'
                AND startdate <= '{endDate:yyyy-MM-dd HH:mm:ss}'
            ";

            // Use the centralized diffing implementation
            DBUtils.DiffingHelper diffHelper = new DBUtils.DiffingHelper(_logger);
            DataTable DiffedUserInteractionData = diffHelper.DiffDataWithCondition(
                CurrentJob,
                whereCondition,
                UserInteractionData,
                DBAdapter,
                "keyid"
            );

            // If there are no rows to write, we're done
            if (DiffedUserInteractionData.Rows.Count == 0)
            {
                _logger?.LogInformation("No new or updated user interaction data to write. Updating last sync date to {Date}.", GCData.UserInteractionLastUpdate);
                // Still update the last sync date even if no rows were written
                Successful = GCData.UpdateLastSuccessDate(GCData.UserInteractionLastUpdate, SyncType);
                if (Successful)
                {
                    _logger?.LogInformation("UserInteraction:MaxSyncDate: Successfully updated MaxSyncDate for {SyncType} to {Date}", SyncType, GCData.UserInteractionLastUpdate);
                }
                else
                {
                    _logger?.LogError("UserInteraction:MaxSyncDate: Failed to update MaxSyncDate for {SyncType} to {Date}", SyncType, GCData.UserInteractionLastUpdate);
                }
                return Successful;
            }

            // Always write to the regular table, not the backfill table
            Successful = DBAdapter.WriteSQLDataBulk(DiffedUserInteractionData, CurrentJob);
            if (Successful)
            {
                _logger?.LogInformation("User interaction data saved. Updating last sync date to {Date}.", GCData.UserInteractionLastUpdate);
                // When in backfill mode, update the backfill table's watermark
                Successful = GCData.UpdateLastSuccessDate(GCData.UserInteractionLastUpdate, SyncType);
                if (Successful)
                {
                    _logger?.LogInformation("UserInteraction:MaxSyncDate: Successfully updated MaxSyncDate for {SyncType} to {Date}", SyncType, GCData.UserInteractionLastUpdate);
                }
                else
                {
                    _logger?.LogError("UserInteraction:MaxSyncDate: Failed to update MaxSyncDate for {SyncType} to {Date}", SyncType, GCData.UserInteractionLastUpdate);
                }
            }
            else
            {
                _logger?.LogWarning("Job:Error: Failed to write user interaction data; sync date not updated");
            }

            _logger?.LogInformation("Job:Complete: {SyncType} job finished in {ElapsedSeconds:N2}s", SyncType, (DateTime.Now - Start).TotalSeconds);
            return Successful;
        }

        public bool UpdateGCQueueInteractionData(bool Backfill = false)
        {
            bool Successful = false;
            string CurrentJob = "queueinteractiondata";
            string BackfillJob = CurrentJob + "_backfill";
            string SyncType = Backfill ? BackfillJob : CurrentJob;
            DateTime Start = DateTime.Now;

            _logger?.LogInformation("Job:Start: Beginning {SyncType} job", SyncType);

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            if (Backfill)
            {
                Successful = BackfillUtils.ConfigureBackfill(CurrentJob, SyncType, GCData, DBAdapter, _logger);

                if (!Successful){
                    return Successful;
                }
            }

            // Get queue data from Genesys
            // Pass granularity parameter (null for default)
            DataTable QueueInteractionData = GCData.QueueInteractionData();
            _logger?.LogInformation("Job:Data: Retrieved {Count} rows from Genesys Cloud for queue interaction", QueueInteractionData.Rows.Count);

            // Diff the data against what's already in the database
            DateTime startDate = GCData.DateToSyncFrom.Subtract(GCData.LookBackSpan);
            DateTime endDate = GCData.DateToSyncFrom.Add(GCData.MaxSpanToSync);

            // Create the where condition for the diffing query
            string whereCondition = $@"
                startdate >= '{startDate.ToString("yyyy-MM-dd HH:mm:ss")}'
                AND startdate <= '{endDate.ToString("yyyy-MM-dd HH:mm:ss")}'
            ";

            // Use the centralized diffing implementation
            DBUtils.DiffingHelper diffHelper = new DBUtils.DiffingHelper(_logger);
            DataTable DiffedQueueInteractionData = diffHelper.DiffDataWithCondition(
                CurrentJob,
                whereCondition,
                QueueInteractionData,
                DBAdapter,
                "keyid"
            );

            // If there are no rows to write, we're done
            if (DiffedQueueInteractionData.Rows.Count == 0)
            {
                _logger?.LogInformation("No new or updated queue interaction data to write. Updating last sync date to {Date}.", GCData.QueueInteractionLastUpdate);
                // Still update the last sync date even if no rows were written
                Successful = GCData.UpdateLastSuccessDate(GCData.QueueInteractionLastUpdate, SyncType);
                return Successful;
            }

            // Always write to the regular table, not the backfill table
            Successful = DBAdapter.WriteSQLDataBulk(DiffedQueueInteractionData, CurrentJob);
            if (Successful)
            {
                _logger?.LogInformation("Queue interaction data saved. Updating last sync date to {Date}.", GCData.QueueInteractionLastUpdate);
                // When in backfill mode, update the backfill table's watermark
                Successful = GCData.UpdateLastSuccessDate(GCData.QueueInteractionLastUpdate, SyncType);
            }
            else
            {
                _logger?.LogWarning("Job:Error: Failed to write queue interaction data; sync date not updated");
            }

            // Note: Database connection will be closed by Program.cs

            _logger?.LogInformation("Job:Complete: {SyncType} job finished in {ElapsedSeconds:N2}s", SyncType, (DateTime.Now - Start).TotalSeconds);
            return Successful;
        }

        #endregion

        #region Detailed/ConvSummary/Participant Data/FlowOutcome

        public async Task<bool> UpdateGCDetailInteractionData(
            string[]? blockAttributes,
            RegexReplacement[]? renameParticipantAttributeNames,
            bool Backfill = false,
            CSG.Adapter.Configuration.RateLimiting? rateLimitingConfig = null)
        {
            bool Successful = true;
            string CurrentJob = "detailedinteractiondata";
            string BackfillJob = CurrentJob + "_backfill";
            string SyncType = Backfill ? BackfillJob : CurrentJob;
            DateTime Start = DateTime.Now;
            _logger?.LogInformation("Job:Start: Beginning {SyncType} job", SyncType);

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);

            // Initialize with minimum sync date logic for comprehensive data synchronization
            InitializeWithMinimumSyncDate(GCData, SyncType, DBAdapter, Backfill);

            if (Backfill)
            {
                Successful = BackfillUtils.ConfigureBackfill(CurrentJob, SyncType, GCData, DBAdapter, _logger);

                if (!Successful){
                    return Successful;
                }
            }

            // Fetch Genesys data
            DataSet? InterData = null;
            try
            {
                InterData = await GCData.DetailInteractionData(renameParticipantAttributeNames, rateLimitingConfig);
                _logger?.LogInformation("Job:Data: Retrieved {Rows} table(s) from Genesys Cloud for detail interaction", InterData?.Tables.Count ?? 0);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Job:Error: Failed retrieving detailed interaction data from Genesys Cloud");
                throw;
            }

            if (InterData == null)
            {
                _logger?.LogWarning("Job:Warning: No data returned from Genesys Cloud for {SyncType}", SyncType);
                return Successful;
            }

            // 1) DetailedInteractionData
            try
            {
                DataTable? DetailInteractionData = InterData.Tables["detailedinteractiondata"];
                if (DetailInteractionData != null && DetailInteractionData.Rows.Count > 0)
                {
                    _logger?.LogInformation("Job:Data: DetailedInteractionData - {Count} rows from Genesys Cloud", DetailInteractionData.Rows.Count);

                    if (DetailInteractionData.Columns.Contains("RecordType"))
                        DetailInteractionData.Columns.Remove("RecordType");

                    TimeSpan adjustedLookBack = (Math.Abs(GCData.LookBackSpan.TotalHours) == 2)
                        ? TimeSpan.FromHours(4)
                        : GCData.LookBackSpan;
                    // DateToSyncFrom is already UTC from GetSyncLastUpdate, no need to convert
                    string StartDate = GCData.DateToSyncFrom.Subtract(adjustedLookBack)
                        .ToString("yyyy-MM-ddTHH:00:00.000Z");
                    string EndDate = GCData.DateToSyncFrom.Add(GCData.MaxSpanToSync)
                        .ToString("yyyy-MM-ddTHH:00:00.000Z");

                    var validStartDates = DetailInteractionData.AsEnumerable()
                        .Where(r => !r.IsNull("conversationstartdate"))
                        .Select(r => r.Field<DateTime>("conversationstartdate"));
                    var validEndDates = DetailInteractionData.AsEnumerable()
                        .Where(r => !r.IsNull("conversationenddate"))
                        .Select(r => r.Field<DateTime>("conversationenddate"));

                    if (!validStartDates.Any() || !validEndDates.Any())
                    {
                        _logger?.LogWarning("Some rows in detailedInteractionData are missing start/end date; skipping this table.");
                    }
                    else
                    {
                        DateTime minConvStart = validStartDates.Min();
                        DateTime maxConvEnd = validEndDates.Max();

                        string minStartString;
                        string maxEndString;
                        int dayDiff = (int)(maxConvEnd - minConvStart).TotalDays;
                        if (dayDiff > MAX_DIFF_DAYS)
                        {
                            _logger?.LogInformation($"The difference is {dayDiff} days, which is greater than {MAX_DIFF_DAYS} days.");
                            minStartString = StartDate;
                            maxEndString = EndDate;
                        }
                        else
                        {
                            _logger?.LogInformation($"Querying {dayDiff} days from DB, which is within the limit of {MAX_DIFF_DAYS} days.");
                            minStartString = minConvStart.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                            maxEndString = maxConvEnd.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                        }

                        string whereCondition = $@"
                            conversationStartDate >= '{minStartString}'
                            AND (conversationenddate <= '{maxEndString}' OR conversationEndDate IS NULL)
                        ";

                        DataTable? DetailInteractionDataDiffed;

                        // Only perform diffing if there are more than 100k rows to process
                        // For smaller datasets, the overhead of diffing is not worth the performance cost
                        if (DetailInteractionData.Rows.Count > DIFFING_THRESHOLD)
                        {
                            _logger?.LogInformation("DetailedInteractionData has {RowCount} rows (>{Threshold}), performing diffing optimization",
                                DetailInteractionData.Rows.Count, DIFFING_THRESHOLD);

                            DetailInteractionDataDiffed = await DiffDataFromDBwithBatchesStreamingAsync(
                                "detailedinteractiondata",
                                whereCondition,
                                DetailInteractionData,
                                DBAdapter,
                                "keyid"
                            );
                        }
                        else
                        {
                            _logger?.LogInformation("DetailedInteractionData has {RowCount} rows (<={Threshold}), skipping diffing and processing all rows",
                                DetailInteractionData.Rows.Count, DIFFING_THRESHOLD);

                            // For small datasets, just use all the data without diffing
                            DetailInteractionDataDiffed = DetailInteractionData.Copy();
                        }

                        DetailInteractionData.Dispose();
                        DetailInteractionData = null;

                        foreach (DataRow row in DetailInteractionDataDiffed.Rows)
                        {
                            if (string.IsNullOrWhiteSpace(row["divisionid"]?.ToString()))
                                row["divisionid"] = "00000000-0000-0000-0000-000000000000";
                        }

                        if (DetailInteractionDataDiffed.Columns.Contains("RecordType"))
                            DetailInteractionDataDiffed.Columns.Remove("RecordType");

                        if (!DBAdapter.WriteSQLDataBulk(DetailInteractionDataDiffed, "detailedinteractiondata"))
                        {
                            _logger?.LogWarning("Error writing 'detailedinteractiondata' => not updating last sync date for this table.");
                            Successful = false;
                        }
                        DetailInteractionDataDiffed.Dispose();
                        DetailInteractionDataDiffed = null;

                        if (Successful)
                        {
                            // Update individual table sync date instead of using the generic DetailInteractionLastUpdate
                            string tableSyncType = Backfill ? "detailedinteractiondata_backfill" : "detailedinteractiondata";
                            Successful = GCData.UpdateLastSuccessDate(GCData.DetailInteractionLastUpdate, tableSyncType);
                            _logger?.LogInformation("Updated last sync date for 'detailedinteractiondata' to {Date}.", GCData.DetailInteractionLastUpdate);
                        }
                    }
                }
                else
                {
                    _logger?.LogInformation("No rows in 'detailedinteractiondata' to sync.");
                    // Still update the sync date even when no data is returned to prevent getting stuck
                    string tableSyncType = Backfill ? "detailedinteractiondata_backfill" : "detailedinteractiondata";
                    bool syncDateUpdated = GCData.UpdateLastSuccessDate(GCData.DetailInteractionLastUpdate, tableSyncType);
                    if (syncDateUpdated)
                    {
                        _logger?.LogInformation("Updated last sync date for 'detailedinteractiondata' to {Date} (no data case).", GCData.DetailInteractionLastUpdate);
                    }
                    else
                    {
                        _logger?.LogWarning("Failed to update sync date for 'detailedinteractiondata' (no data case).");
                        Successful = false;
                    }
                }
                InterData.Tables.Remove("detailedinteractiondata");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing detailedInteractionData.");
                Successful = false;
                throw;
            }

            // 2) convsummaryData
            try
            {
                DataTable? ConvSummaryData = InterData.Tables["convsummaryData"];
                if (ConvSummaryData != null && ConvSummaryData.Rows.Count > 0)
                {
                    // Update processing counter
                    lock (_participantProgressLock)
                    {
                        _totalConvSummaryProcessed += ConvSummaryData.Rows.Count;
                    }

                    if (ConvSummaryData.Columns.Contains("RecordType"))
                        ConvSummaryData.Columns.Remove("RecordType");

                    var validStartDates = ConvSummaryData.AsEnumerable()
                        .Where(r => !r.IsNull("conversationstartdate"))
                        .Select(r => r.Field<DateTime>("conversationstartdate"));
                    var validEndDates = ConvSummaryData.AsEnumerable()
                        .Where(r => !r.IsNull("conversationenddate"))
                        .Select(r => r.Field<DateTime>("conversationenddate"));

                    if (!validStartDates.Any() || !validEndDates.Any())
                    {
                        _logger?.LogWarning("Some rows in convsummaryData are missing start/end date; skipping this table.");
                    }
                    else
                    {
                        DateTime minConvStart = validStartDates.Min();
                        DateTime maxConvEnd = validEndDates.Max();

                        // DateToSyncFrom is already UTC from GetSyncLastUpdate, no need to convert
                        string StartDate = GCData.DateToSyncFrom
                            .Subtract(GCData.LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
                        string EndDate = GCData.DateToSyncFrom
                            .Add(GCData.MaxSpanToSync).ToString("yyyy-MM-ddTHH:00:00.000Z");

                        string minStartString;
                        string maxEndString;
                        int dayDiff = (int)(maxConvEnd - minConvStart).TotalDays;
                        if (dayDiff > MAX_DIFF_DAYS)
                        {
                            minStartString = StartDate;
                            maxEndString = EndDate;
                        }
                        else
                        {
                            minStartString = minConvStart.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                            maxEndString = maxConvEnd.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                        }

                        _logger?.LogInformation("ConvSummaryData => {Count} rows from Genesys Cloud.", ConvSummaryData.Rows.Count);

                        string whereCondition = $@"
                            conversationStartDate >= '{minStartString}'
                            AND (conversationenddate <= '{maxEndString}' OR conversationEndDate IS NULL)
                        ";

                        DataTable? ConvSummaryDataDiffed;

                        // Only perform diffing if there are more than 100k rows to process
                        // For smaller datasets, the overhead of diffing is not worth the performance cost
                        if (ConvSummaryData.Rows.Count > DIFFING_THRESHOLD)
                        {
                            _logger?.LogInformation("ConvSummaryData has {RowCount} rows (>{Threshold}), performing diffing optimization",
                                ConvSummaryData.Rows.Count, DIFFING_THRESHOLD);

                            ConvSummaryDataDiffed = await DiffDataFromDBwithBatchesStreamingAsync(
                                "convsummarydata",
                                whereCondition,
                                ConvSummaryData,
                                DBAdapter,
                                "keyid"
                            );
                        }
                        else
                        {
                            _logger?.LogInformation("ConvSummaryData has {RowCount} rows (<={Threshold}), skipping diffing and processing all rows",
                                ConvSummaryData.Rows.Count, DIFFING_THRESHOLD);

                            // For small datasets, just use all the data without diffing
                            ConvSummaryDataDiffed = ConvSummaryData.Copy();
                        }
                        ConvSummaryData.Dispose();
                        ConvSummaryData = null;

                        foreach (DataRow row in ConvSummaryDataDiffed.Rows)
                        {
                            if (string.IsNullOrWhiteSpace(row["divisionid"]?.ToString()))
                                row["divisionid"] = "00000000-0000-0000-0000-000000000000";
                        }

                        if (ConvSummaryDataDiffed.Columns.Contains("RecordType"))
                            ConvSummaryDataDiffed.Columns.Remove("RecordType");

                        int rowsToWrite = ConvSummaryDataDiffed.Rows.Count;
                        bool writeSuccess = DBAdapter.WriteSQLDataBulk(ConvSummaryDataDiffed, "convsummarydata");

                        // Update counters based on write success
                        lock (_participantProgressLock)
                        {
                            if (writeSuccess)
                            {
                                _totalConvSummaryWritten += rowsToWrite;
                            }
                            else
                            {
                                _totalConvSummaryErrors += rowsToWrite;
                            }
                        }

                        if (!writeSuccess)
                        {
                            _logger?.LogWarning("Error writing 'convsummarydata' => not updating last sync date for this table.");
                            Successful = false;
                        }
                        ConvSummaryDataDiffed.Dispose();
                        ConvSummaryDataDiffed = null;

                        if (Successful)
                        {
                            // Update individual table sync date instead of using the generic DetailInteractionLastUpdate
                            string tableSyncType = Backfill ? "convsummarydata_backfill" : "convsummarydata";
                            Successful = GCData.UpdateLastSuccessDate(GCData.DetailInteractionLastUpdate, tableSyncType);
                            _logger?.LogInformation("Updated last sync date for convsummarydata to {Date}.", GCData.DetailInteractionLastUpdate);
                        }
                    }
                }
                else
                {
                    _logger?.LogInformation("No rows in 'convsummarydata' to sync.");
                    // Still update the sync date to match detailedinteractiondata to prevent getting stuck
                    string tableSyncType = Backfill ? "convsummarydata_backfill" : "convsummarydata";
                    bool syncDateUpdated = GCData.UpdateLastSuccessDate(GCData.DetailInteractionLastUpdate, tableSyncType);
                    if (syncDateUpdated)
                    {
                        _logger?.LogInformation("Updated last sync date for 'convsummarydata' to {Date} (no data case).", GCData.DetailInteractionLastUpdate);
                    }
                    else
                    {
                        _logger?.LogWarning("Failed to update sync date for 'convsummarydata' (no data case).");
                        Successful = false;
                    }
                }
                InterData.Tables.Remove("convsummaryData");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing convsummaryData.");
                Successful = false;
                throw;
            }

            // 3) participantAttributesDynamic
            try
            {
                DataTable? ParticipantAttributes = InterData.Tables["participantAttributesDynamic"];
                if (ParticipantAttributes != null && ParticipantAttributes.Rows.Count > 0)
                {
                    // Update processing counter
                    lock (_participantProgressLock)
                    {
                        _totalParticipantAttributesProcessed += ParticipantAttributes.Rows.Count;
                    }

                    if (ParticipantAttributes.Columns.Contains("RecordType"))
                        ParticipantAttributes.Columns.Remove("RecordType");

                    if (blockAttributes != null && blockAttributes.Length > 0)
                    {
                        var colsToRemove = ParticipantAttributes.Columns
                            .Cast<DataColumn>()
                            .Where(c => blockAttributes.Any(r => Regex.IsMatch(c.ColumnName, r)))
                            .ToList();
                        foreach (var col in colsToRemove)
                        {
                            _logger?.LogDebug("ParticipantAttributes => removing blocked column: {ColName}", col.ColumnName);
                            ParticipantAttributes.Columns.Remove(col);
                        }
                    }

                    if (DBAdapter.DBType == DatabaseType.Snowflake)
                        UCAUtils.HandleSnowflakeColumnNames(ParticipantAttributes);

                    var validStartDates = ParticipantAttributes.AsEnumerable()
                        .Where(r => !r.IsNull("conversationstartdate"))
                        .Select(r => r.Field<DateTime>("conversationstartdate"));
                    var validEndDates = ParticipantAttributes.AsEnumerable()
                        .Where(r => !r.IsNull("conversationenddate"))
                        .Select(r => r.Field<DateTime>("conversationenddate"));

                    if (!validStartDates.Any() || !validEndDates.Any())
                    {
                        _logger?.LogWarning("Some rows in participantAttributesDynamic are missing start/end date; skipping this table.");
                    }
                    else
                    {
                        DateTime minConvStart = validStartDates.Min();
                        DateTime maxConvEnd = validEndDates.Max();

                        // DateToSyncFrom is already UTC from GetSyncLastUpdate, no need to convert
                        string StartDate = GCData.DateToSyncFrom.Subtract(GCData.LookBackSpan)
                            .ToString("yyyy-MM-ddTHH:00:00.000Z");
                        string EndDate = GCData.DateToSyncFrom.Add(GCData.MaxSpanToSync)
                            .ToString("yyyy-MM-ddTHH:00:00.000Z");

                        string minStartString;
                        string maxEndString;
                        int dayDiff = (int)(maxConvEnd - minConvStart).TotalDays;
                        if (dayDiff > MAX_DIFF_DAYS)
                        {
                            minStartString = StartDate;
                            maxEndString = EndDate;
                        }
                        else
                        {
                            minStartString = minConvStart.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                            maxEndString = maxConvEnd.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                        }

                        string whereCondition = $@"
                            conversationStartDate >= '{minStartString}'
                            AND (conversationenddate <= '{maxEndString}' OR conversationEndDate IS NULL)
                        ";

                        DataTable? ParticipantAttributesDiffed;

                        // Only perform diffing if there are more than 100k rows to process
                        // For smaller datasets, the overhead of diffing is not worth the performance cost
                        if (ParticipantAttributes.Rows.Count > DIFFING_THRESHOLD)
                        {
                            _logger?.LogInformation("ParticipantAttributes has {RowCount} rows (>{Threshold}), performing diffing optimization",
                                ParticipantAttributes.Rows.Count, DIFFING_THRESHOLD);

                            ParticipantAttributesDiffed = await DiffDataFromDBwithBatchesStreamingAsync(
                                "participantattributesdynamic",
                                whereCondition,
                                ParticipantAttributes,
                                DBAdapter,
                                "keyid"
                            );
                        }
                        else
                        {
                            _logger?.LogInformation("ParticipantAttributes has {RowCount} rows (<={Threshold}), skipping diffing and processing all rows",
                                ParticipantAttributes.Rows.Count, DIFFING_THRESHOLD);

                            // For small datasets, just use all the data without diffing
                            ParticipantAttributesDiffed = ParticipantAttributes.Copy();
                        }
                        ParticipantAttributes.Dispose();
                        ParticipantAttributes = null;

                        if (ParticipantAttributesDiffed.Columns.Contains("RecordType"))
                            ParticipantAttributesDiffed.Columns.Remove("RecordType");

                        int rowsToWrite = ParticipantAttributesDiffed.Rows.Count;
                        bool writeSuccess = DBAdapter.WriteDynamicSQLData(ParticipantAttributesDiffed, "participantattributesdynamic");

                        // Update counters based on write success
                        lock (_participantProgressLock)
                        {
                            if (writeSuccess)
                            {
                                _totalParticipantAttributesWritten += rowsToWrite;
                            }
                            else
                            {
                                _totalParticipantAttributesErrors += rowsToWrite;
                            }
                        }

                        if (!writeSuccess)
                        {
                            _logger?.LogWarning("Error writing participantAttributesDynamic => not updating last sync date for this table.");
                            Successful = false;
                        }

                        ParticipantAttributesDiffed.Dispose();
                        ParticipantAttributesDiffed = null;

                        if (Successful)
                        {
                            // Update individual table sync date instead of using the generic DetailInteractionLastUpdate
                            string tableSyncType = Backfill ? "participantattributesdynamic_backfill" : "participantattributesdynamic";
                            Successful = GCData.UpdateLastSuccessDate(GCData.DetailInteractionLastUpdate, tableSyncType);
                            _logger?.LogInformation("Updated last sync date for participantattributesdynamic to {Date}.", GCData.DetailInteractionLastUpdate);
                        }

                        // Log progress summary
                        LogParticipantProgressSummary();
                    }
                }
                else
                {
                    _logger?.LogInformation("No rows in 'participantattributesdynamic' to sync.");
                    // Still update the sync date to match detailedinteractiondata to prevent getting stuck
                    string tableSyncType = Backfill ? "participantattributesdynamic_backfill" : "participantattributesdynamic";
                    bool syncDateUpdated = GCData.UpdateLastSuccessDate(GCData.DetailInteractionLastUpdate, tableSyncType);
                    if (syncDateUpdated)
                    {
                        _logger?.LogInformation("Updated last sync date for 'participantattributesdynamic' to {Date} (no data case).", GCData.DetailInteractionLastUpdate);
                    }
                    else
                    {
                        _logger?.LogWarning("Failed to update sync date for 'participantattributesdynamic' (no data case).");
                        Successful = false;
                    }
                }
                InterData.Tables.Remove("participantAttributesDynamic");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing participantAttributesDynamic.");
                Successful = false;
                throw;
            }

            // 4) participantSummaryData
            try
            {
                DataTable? ParticipantSummary = InterData.Tables["participantsummaryData"];
                if (ParticipantSummary != null && ParticipantSummary.Rows.Count > 0)
                {
                    // Update processing counter
                    lock (_participantProgressLock)
                    {
                        _totalParticipantSummaryProcessed += ParticipantSummary.Rows.Count;
                    }

                    _logger?.LogInformation("ParticipantSummary:Start: Processing {RowCount} participant summary rows", ParticipantSummary.Rows.Count);

                    if (ParticipantSummary.Columns.Contains("RecordType"))
                        ParticipantSummary.Columns.Remove("RecordType");

                    DataRow[] removeRows = ParticipantSummary.Select("purpose in ('ivr','workflow','external') or purpose is null");
                    foreach (DataRow row in removeRows)
                        ParticipantSummary.Rows.Remove(row);
                    if (ParticipantSummary.Columns.Contains("null"))
                        ParticipantSummary.Columns.Remove("null");
                    ParticipantSummary.AcceptChanges();

                    var validStartDates = ParticipantSummary.AsEnumerable()
                        .Where(r => !r.IsNull("conversationstartdate"))
                        .Select(r => r.Field<DateTime>("conversationstartdate"));
                    var validEndDates = ParticipantSummary.AsEnumerable()
                        .Where(r => !r.IsNull("conversationenddate"))
                        .Select(r => r.Field<DateTime>("conversationenddate"));

                    if (!validStartDates.Any() || !validEndDates.Any())
                    {
                        _logger?.LogWarning("Some rows in participantSummaryData are missing start/end date; skipping this table.");
                    }
                    else
                    {
                        DateTime minConvStart = validStartDates.Min();
                        DateTime maxConvEnd = validEndDates.Max();

                        // DateToSyncFrom is already UTC from GetSyncLastUpdate, no need to convert
                        string StartDate = GCData.DateToSyncFrom.Subtract(GCData.LookBackSpan)
                            .ToString("yyyy-MM-ddTHH:00:00.000Z");
                        string EndDate = GCData.DateToSyncFrom.Add(GCData.MaxSpanToSync)
                            .ToString("yyyy-MM-ddTHH:00:00.000Z");

                        string minStartString;
                        string maxEndString;
                        int dayDiff = (int)(maxConvEnd - minConvStart).TotalDays;
                        if (dayDiff > MAX_DIFF_DAYS)
                        {
                            minStartString = StartDate;
                            maxEndString = EndDate;
                        }
                        else
                        {
                            minStartString = minConvStart.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                            maxEndString = maxConvEnd.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                        }

                        string whereCondition = $@"
                            conversationStartDate >= '{minStartString}'
                            AND (conversationEndDate <= '{maxEndString}' OR conversationEndDate IS NULL)
                        ";

                        DataTable? ParticipantSummaryDiffed;

                        // Only perform diffing if there are more than 100k rows to process
                        // For smaller datasets, the overhead of diffing is not worth the performance cost
                        if (ParticipantSummary.Rows.Count > DIFFING_THRESHOLD)
                        {
                            _logger?.LogInformation("ParticipantSummary has {RowCount} rows (>{Threshold}), performing diffing optimization",
                                ParticipantSummary.Rows.Count, DIFFING_THRESHOLD);

                            ParticipantSummaryDiffed = await DiffDataFromDBwithBatchesStreamingAsync(
                                "participantsummarydata",
                                whereCondition,
                                ParticipantSummary,
                                DBAdapter,
                                "keyid"
                            );
                        }
                        else
                        {
                            _logger?.LogInformation("ParticipantSummary has {RowCount} rows (<={Threshold}), skipping diffing and processing all rows",
                                ParticipantSummary.Rows.Count, DIFFING_THRESHOLD);

                            // For small datasets, just use all the data without diffing
                            ParticipantSummaryDiffed = ParticipantSummary.Copy();
                        }
                        ParticipantSummary.Dispose();
                        ParticipantSummary = null;

                        foreach (DataRow row in ParticipantSummaryDiffed.Rows)
                        {
                            if (string.IsNullOrWhiteSpace(row["divisionid"]?.ToString()))
                                row["divisionid"] = "00000000-0000-0000-0000-000000000000";
                        }

                        if (ParticipantSummaryDiffed.Columns.Contains("RecordType"))
                            ParticipantSummaryDiffed.Columns.Remove("RecordType");

                        int rowsToWrite = ParticipantSummaryDiffed.Rows.Count;
                        bool writeSuccess = DBAdapter.WriteSQLDataBulk(ParticipantSummaryDiffed, "participantsummarydata");

                        // Update counters based on write success
                        lock (_participantProgressLock)
                        {
                            if (writeSuccess)
                            {
                                _totalParticipantSummaryWritten += rowsToWrite;
                            }
                            else
                            {
                                _totalParticipantSummaryErrors += rowsToWrite;
                            }
                        }

                        if (!writeSuccess)
                        {
                            _logger?.LogWarning("ParticipantSummary:Error: Failed to write {RowCount} participant summary rows => not updating last sync date for this table.", rowsToWrite);
                            Successful = false;
                        }
                        else
                        {
                            _logger?.LogInformation("ParticipantSummary:Success: Successfully wrote {RowCount} participant summary rows", rowsToWrite);
                        }

                        ParticipantSummaryDiffed.Dispose();
                        ParticipantSummaryDiffed = null;

                        if (Successful)
                        {
                            // Update individual table sync date instead of using the generic DetailInteractionLastUpdate
                            string tableSyncType = Backfill ? "participantsummarydata_backfill" : "participantsummarydata";
                            Successful = GCData.UpdateLastSuccessDate(GCData.DetailInteractionLastUpdate, tableSyncType);
                            _logger?.LogInformation("ParticipantSummary:SyncDate: Updated last sync date for participantsummarydata to {Date}.", GCData.DetailInteractionLastUpdate);
                        }

                        // Log progress summary
                        LogParticipantProgressSummary();
                    }
                }
                else
                {
                    _logger?.LogInformation("No rows in 'participantsummarydata' to sync.");
                    // Still update the sync date to match detailedinteractiondata to prevent getting stuck
                    string tableSyncType = Backfill ? "participantsummarydata_backfill" : "participantsummarydata";
                    bool syncDateUpdated = GCData.UpdateLastSuccessDate(GCData.DetailInteractionLastUpdate, tableSyncType);
                    if (syncDateUpdated)
                    {
                        _logger?.LogInformation("Updated last sync date for 'participantsummarydata' to {Date} (no data case).", GCData.DetailInteractionLastUpdate);
                    }
                    else
                    {
                        _logger?.LogWarning("Failed to update sync date for 'participantsummarydata' (no data case).");
                        Successful = false;
                    }
                }
                InterData.Tables.Remove("participantsummaryData");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing participantSummaryData.");
                Successful = false;
                throw;
            }

            // 5) FlowOutcomeData
            try
            {
                DataTable? FlowOutcomeData = InterData.Tables["flowoutcomedata"];
                if (FlowOutcomeData != null && FlowOutcomeData.Rows.Count > 0)
                {
                    // Update processing counter
                    lock (_participantProgressLock)
                    {
                        _totalFlowOutcomeProcessed += FlowOutcomeData.Rows.Count;
                    }

                    _logger?.LogInformation("FlowOutcome:Start: Processing {RowCount} flow outcome rows", FlowOutcomeData.Rows.Count);

                    if (FlowOutcomeData.Columns.Contains("RecordType"))
                        FlowOutcomeData.Columns.Remove("RecordType");

                    var validStartDates = FlowOutcomeData.AsEnumerable()
                        .Where(r => !r.IsNull("conversationstartdate"))
                        .Select(r => r.Field<DateTime>("conversationstartdate"));
                    var validEndDates = FlowOutcomeData.AsEnumerable()
                        .Where(r => !r.IsNull("conversationenddate"))
                        .Select(r => r.Field<DateTime>("conversationenddate"));

                    // For flowoutcome data, only require start dates (end dates can be null for ongoing flows)
                    if (!validStartDates.Any())
                    {
                        _logger?.LogWarning("Some rows in flowoutcomedata are missing start date; skipping this table.");
                    }
                    else
                    {
                        DateTime minConvStart = validStartDates.Min();
                        // Use max end date if available, otherwise use max start date as fallback for ongoing flows
                        DateTime maxConvEnd = validEndDates.Any() ? validEndDates.Max() : validStartDates.Max();

                        // DateToSyncFrom is already UTC from GetSyncLastUpdate, no need to convert
                        string StartDate = GCData.DateToSyncFrom.Subtract(GCData.LookBackSpan)
                            .ToString("yyyy-MM-ddTHH:00:00.000Z");
                        string EndDate = GCData.DateToSyncFrom.Add(GCData.MaxSpanToSync)
                            .ToString("yyyy-MM-ddTHH:00:00.000Z");

                        string minStartString;
                        string maxEndString;
                        int dayDiff = (int)(maxConvEnd - minConvStart).TotalDays;
                        if (dayDiff > MAX_DIFF_DAYS)
                        {
                            minStartString = StartDate;
                            maxEndString = EndDate;
                        }
                        else
                        {
                            minStartString = minConvStart.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                            maxEndString = maxConvEnd.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                        }

                        string whereCondition = $@"
                            conversationStartDate >= '{minStartString}'
                            AND (conversationEndDate <= '{maxEndString}' OR conversationEndDate IS NULL)
                        ";

                        DataTable? FlowOutcomeDataDiffed;

                        // Only perform diffing if there are more than 100k rows to process
                        // For smaller datasets, the overhead of diffing is not worth the performance cost
                        if (FlowOutcomeData.Rows.Count > DIFFING_THRESHOLD)
                        {
                            _logger?.LogInformation("FlowOutcomeData has {RowCount} rows (>{Threshold}), performing diffing optimization",
                                FlowOutcomeData.Rows.Count, DIFFING_THRESHOLD);

                            FlowOutcomeDataDiffed = await DiffDataFromDBwithBatchesStreamingAsync(
                                "flowoutcomedata",
                                whereCondition,
                                FlowOutcomeData,
                                DBAdapter,
                                "keyid"
                            );
                        }
                        else
                        {
                            _logger?.LogInformation("FlowOutcomeData has {RowCount} rows (<={Threshold}), skipping diffing and processing all rows",
                                FlowOutcomeData.Rows.Count, DIFFING_THRESHOLD);

                            // For small datasets, just use all the data without diffing
                            FlowOutcomeDataDiffed = FlowOutcomeData.Copy();
                        }
                        FlowOutcomeData.Dispose();
                        FlowOutcomeData = null;

                        if (FlowOutcomeDataDiffed.Columns.Contains("RecordType"))
                            FlowOutcomeDataDiffed.Columns.Remove("RecordType");

                        int rowsToWrite = FlowOutcomeDataDiffed.Rows.Count;
                        bool writeSuccess = DBAdapter.WriteSQLDataBulk(FlowOutcomeDataDiffed, "flowoutcomedata");

                        // Update counters based on write success
                        lock (_participantProgressLock)
                        {
                            if (writeSuccess)
                            {
                                _totalFlowOutcomeWritten += rowsToWrite;
                            }
                            else
                            {
                                _totalFlowOutcomeErrors += rowsToWrite;
                            }
                        }

                        if (!writeSuccess)
                        {
                            _logger?.LogWarning("FlowOutcome:Error: Failed to write {RowCount} flow outcome rows => not updating last sync date for this table.", rowsToWrite);
                            Successful = false;
                        }
                        else
                        {
                            _logger?.LogInformation("FlowOutcome:Success: Successfully wrote {RowCount} flow outcome rows", rowsToWrite);
                        }

                        FlowOutcomeDataDiffed.Dispose();
                        FlowOutcomeDataDiffed = null;

                        if (Successful)
                        {
                            // Update individual table sync date instead of using the generic DetailInteractionLastUpdate
                            string tableSyncType = Backfill ? "flowoutcomedata_backfill" : "flowoutcomedata";
                            Successful = GCData.UpdateLastSuccessDate(GCData.DetailInteractionLastUpdate, tableSyncType);
                            _logger?.LogInformation("FlowOutcome:SyncDate: Updated last sync date for flowoutcomedata to {Date}.", GCData.DetailInteractionLastUpdate);
                        }

                        // Log progress summary
                        LogParticipantProgressSummary();
                    }
                }
                else
                {
                    _logger?.LogInformation("No rows in 'flowoutcomedata' to sync.");
                    // Still update the sync date to match detailedinteractiondata to prevent getting stuck
                    string tableSyncType = Backfill ? "flowoutcomedata_backfill" : "flowoutcomedata";
                    bool syncDateUpdated = GCData.UpdateLastSuccessDate(GCData.DetailInteractionLastUpdate, tableSyncType);
                    if (syncDateUpdated)
                    {
                        _logger?.LogInformation("Updated last sync date for 'flowoutcomedata' to {Date} (no data case).", GCData.DetailInteractionLastUpdate);
                    }
                    else
                    {
                        _logger?.LogWarning("Failed to update sync date for 'flowoutcomedata' (no data case).");
                        Successful = false;
                    }
                }
                // FIXED: Check if table exists before removing to prevent ArgumentException
                if (InterData.Tables.Contains("flowoutcomedata"))
                {
                    InterData.Tables.Remove("flowoutcomedata");
                    _logger?.LogDebug("Removed 'flowoutcomedata' table from InterData DataSet");
                }
                else
                {
                    _logger?.LogDebug("'flowoutcomedata' table not found in InterData DataSet (likely no data to process)");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing flowoutcomedata.");
                Successful = false;
                throw;
            }

            _logger?.LogDebug("Update Date: {UpdateDate}", GCData.DetailInteractionLastUpdate);

            // Log final participant processing summary
            LogParticipantProgressSummary(true);

            // Validate data consistency between conversation summary and participant attributes processing
            ValidateDataConsistency(SyncType);

            // Reset participant processing counters for the next job run
            lock (_participantProgressLock)
            {
                int totalProcessed = _totalParticipantAttributesProcessed + _totalParticipantSummaryProcessed + _totalFlowOutcomeProcessed;
                int totalWritten = _totalParticipantAttributesWritten + _totalParticipantSummaryWritten + _totalFlowOutcomeWritten;
                int totalErrors = _totalParticipantAttributesErrors + _totalParticipantSummaryErrors + _totalFlowOutcomeErrors;

                _logger?.LogInformation(
                    "Participant:Summary: Job completed - Processed {TotalProcessed} rows, Written {TotalWritten} rows, Errors {TotalErrors} rows | " +
                    "ParticipantAttributes: {PAProcessed}/{PAWritten}/{PASkipped}/{PAErrors} | " +
                    "ParticipantSummary: {PSProcessed}/{PSWritten}/{PSErrors} | " +
                    "FlowOutcome: {FOProcessed}/{FOWritten}/{FOErrors}",
                    totalProcessed,
                    totalWritten,
                    totalErrors,
                    _totalParticipantAttributesProcessed,
                    _totalParticipantAttributesWritten,
                    _totalParticipantAttributesSkipped,
                    _totalParticipantAttributesErrors,
                    _totalParticipantSummaryProcessed,
                    _totalParticipantSummaryWritten,
                    _totalParticipantSummaryErrors,
                    _totalFlowOutcomeProcessed,
                    _totalFlowOutcomeWritten,
                    _totalFlowOutcomeErrors);

                // Reset all counters for the next job run
                _totalConvSummaryProcessed = 0;
                _totalConvSummaryWritten = 0;
                _totalConvSummaryErrors = 0;
                _totalParticipantAttributesProcessed = 0;
                _totalParticipantAttributesWritten = 0;
                _totalParticipantAttributesSkipped = 0;
                _totalParticipantAttributesErrors = 0;
                _totalParticipantSummaryProcessed = 0;
                _totalParticipantSummaryWritten = 0;
                _totalParticipantSummaryErrors = 0;
                _totalFlowOutcomeProcessed = 0;
                _totalFlowOutcomeWritten = 0;
                _totalFlowOutcomeErrors = 0;
            }

            // Note: Database connection will be closed by Program.cs

            _logger?.LogInformation("{SyncType} job completed in {ElapsedSeconds} seconds.", SyncType, (DateTime.Now - Start).TotalSeconds);

            return Successful;
        }

        #endregion

        #region Minimum Sync Date Logic

        /// <summary>
        /// Initializes GCData with the minimum sync date among all interaction tables to ensure comprehensive data synchronization.
        /// This prevents data gaps by starting from the earliest table's last sync point.
        /// </summary>
        private void InitializeWithMinimumSyncDate(GCGetData gcData, string syncType, DBUtils.DBUtils dbAdapter, bool backfill)
        {
            // Define all interaction table names
            string[] interactionTables = backfill
                ? new[] { "detailedinteractiondata_backfill", "convsummarydata_backfill", "participantattributesdynamic_backfill", "participantsummarydata_backfill", "flowoutcomedata_backfill" }
                : new[] { "detailedinteractiondata", "convsummarydata", "participantattributesdynamic", "participantsummarydata", "flowoutcomedata" };

            // Get sync dates for all tables
            var tableSyncDates = new Dictionary<string, DateTime>();
            foreach (string tableName in interactionTables)
            {
                DateTime syncDate = dbAdapter.GetSyncLastUpdate(tableName);
                tableSyncDates[tableName] = syncDate;
            }

            // Find the minimum (earliest) sync date
            DateTime minimumSyncDate = tableSyncDates.Values.Min();
            string tableWithMinDate = tableSyncDates.FirstOrDefault(kvp => kvp.Value == minimumSyncDate).Key;

            // Condensed logging: Show all table sync dates in a single log entry
            var tableDatesInfo = string.Join(", ", tableSyncDates.Select(kvp => $"{kvp.Key}:{kvp.Value:yyyy-MM-ddTHH:mm:ss.fffZ}"));
            _logger?.LogInformation("Interaction:Sync: Using minimum sync date {MinDate} from '{TableName}' | All tables: {TableDates}",
                minimumSyncDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), tableWithMinDate, tableDatesInfo);

            // Initialize GCData with the primary sync type but override the DateToSyncFrom with minimum date
            gcData.Initialize(syncType);
            gcData.DateToSyncFrom = minimumSyncDate;
        }

        #endregion

        #region Offset-based Diff with Streaming/Batches

        /// <summary>
        /// Processes DB rows in batches, comparing each batch against the GC data,
        /// and removes GC rows that match (i.e. require no update).
        /// Returns a DataTable that contains only GC rows that are new or require updates.
        /// </summary>
        public async Task<DataTable> DiffDataFromDBwithBatchesStreamingAsync(
            string tableName,
            string dateQueryCondition,
            DataTable gcDataTable,
            DBUtils.DBUtils DBAdapter,
            string orderByColumn = "keyid")
        {
            // Use the centralized DiffingHelper implementation
            var diffingHelper = new DBUtils.DiffingHelper(_logger);
            return diffingHelper.DiffDataWithCondition(
                tableName,
                dateQueryCondition,
                gcDataTable,
                DBAdapter,
                orderByColumn
            );
        }


        #endregion

        #region Voice Analysis (Async Refactored)

        public bool UpdateGCVoiceAnalysisData(bool backfill = false)


           {
            return UpdateGCVoiceAnalysisDataAsync(backfill).GetAwaiter().GetResult();
        }

        public async Task<bool> UpdateGCVoiceAnalysisDataAsync(bool Backfill = false)
        {
            bool Successful = false;

            string currentJob = "convvoiceoverviewdata";
            string backfillJob = "convvoiceoverviewdata_backfill";
            string SyncType = Backfill ? backfillJob : currentJob;

            DateTime Start = DateTime.Now;
            Stopwatch watch = Stopwatch.StartNew();

            // Track batch processing success to prevent sync date updates on failures
            bool allBatchesSuccessful = true;
            int totalBatches = 0;
            int failedBatches = 0;

            _logger?.LogInformation("Starting job: {SyncType}", SyncType);

            // Check for Knowledge Quest license
            try
            {
                // Use the license manager to check if the client has the Knowledge Quest license
                _enableKnowledgeQuest = CSG.Adapter.Licensing.LicenseManager.HasKnowledgeQuestLicense(_logger);

                _logger?.LogInformation("Voice:License: Knowledge Quest license is {Status}",
                    _enableKnowledgeQuest ? "enabled" : "disabled");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Voice:License: Error checking Knowledge Quest license, feature will be disabled");
                _enableKnowledgeQuest = false;
            }

            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DBAdapter.Initialize();

            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            bool finalResult = true;
            if (Backfill)
            {
                finalResult = BackfillUtils.ConfigureBackfill(currentJob, SyncType, GCData, DBAdapter, _logger);

                if (!finalResult){
                    return finalResult;
                }
            }

            DateTime lastSyncInteractions = DBAdapter.GetSyncLastUpdate("detailedinteractiondata".ToLower());
            DateTime nowTime = DateTime.UtcNow.Subtract(TimeSpan.FromHours(4));
            DateTime lastSyncDependencies = new[] { lastSyncInteractions, nowTime }.Min();
            _logger?.LogInformation(
                "{Job}: Dependency => interaction {InteractionSync}Z, min {MinSync}Z",
                currentJob, lastSyncInteractions.ToString("s"), lastSyncDependencies.ToString("s")
            );

            if (lastSyncDependencies > GCData.DateToSyncFrom.Add(GCData.MaxSpanToSync))
                lastSyncDependencies = GCData.DateToSyncFrom.Add(GCData.MaxSpanToSync);

            DateTime syncFrom = GCData.DateToSyncFrom.AddMinutes(-1);
            DateTime syncTo = lastSyncDependencies.AddMinutes(-1);
            if (syncFrom >= syncTo)
            {
                _logger?.LogInformation(
                    "{Job}: Sync {SyncFrom}Z >= dependency {SyncTo}Z => nothing to do",
                    currentJob, syncFrom.ToString("s"), syncTo.ToString("s")
                );
                return false;
            }

            if (Backfill)
            {
                if (GCData.DateToSyncFrom == DateTime.MaxValue)
                    return false;
                DateTime currentSyncWatermark = DBAdapter.GetSyncLastUpdate(currentJob);
                if (currentSyncWatermark.Subtract(GCData.DateToSyncFrom) <= TimeSpan.FromDays(2))
                {
                    _logger?.LogWarning(
                        "Backfill job => caught up with current job. (Backfill={0}, Current={1})",
                        GCData.DateToSyncFrom, currentSyncWatermark
                    );
                    GCData.DateToSyncFrom = DateTime.MaxValue;
                    GCData.UpdateLastSuccessDate(GCData.DateToSyncFrom, SyncType);
                    return false;
                }
            }

            // Build query based on DB type.
            TimeSpan MaxDaysToSync = GCData.MaxSpanToSync;
            _logger?.LogInformation(
                "Date={0}, maxSpan={1}, programSpan={2}",
                GCData.DateToSyncFrom, GCData.MaxSpanToSync, MaxDaysToSync
            );

            string SelectString;
            switch (DBAdapter.DBType)
            {
                case DatabaseType.Snowflake:
                    SelectString = "select distinct conversationid,peer,'n' as gettransscript "
                        + "from convSummaryData where conversationenddate between dateadd(HOUR,-3,TO_TIMESTAMP_NTZ('"
                        + GCData.DateToSyncFrom.ToString("MM/dd/yyyy hh:mm:ss") + "')) and dateadd(DAY,"
                        + MaxDaysToSync.TotalDays + ",TO_TIMESTAMP_NTZ('"
                        + GCData.DateToSyncFrom.ToString("MM/dd/yyyy hh:mm:ss") + "')) "
                        + "and (peer IS NOT NULL and peer!='') and firstmediatype in('voice','callback');";
                    break;
                case DatabaseType.MSSQL:
                    SelectString = "select distinct conversationid,peer,'n' as gettransscript "
                        + "from convSummaryData where conversationenddate between dateadd(HOUR,-3,'"
                        + GCData.DateToSyncFrom + "') and dateadd(DAY,"
                        + MaxDaysToSync.TotalDays + ",'"
                        + GCData.DateToSyncFrom
                        + "') and (peer IS NOT NULL and peer!='') and firstmediatype in('voice','callback');";
                    break;
                case DatabaseType.MySQL:
                    SelectString = "select distinct conversationid,peer,'n' as gettransscript "
                        + "from convSummaryData where conversationenddate between date_add('"
                        + GCData.DateToSyncFrom + "', INTERVAL -1 HOUR) and date_add('"
                        + GCData.DateToSyncFrom + "', INTERVAL "
                        + MaxDaysToSync.TotalDays + " DAY) "
                        + "and (peer is not null and peer!='') and firstmediatype in('voice','callback');";
                    break;
                case DatabaseType.PostgreSQL:
                    SelectString = "select distinct di.conversationid,di.peer,'n' as gettransscript "
                        + "from detailedinteractionData di "
                        + "inner join queuedetails qd on qd.id=di.queueid and qd.enabletranscription=true "
                        + "where (di.conversationenddate between '"
                        + GCData.DateToSyncFrom + "'::timestamp - 1* interval '1 hour' and '"
                        + GCData.DateToSyncFrom + "'::timestamp + "
                        + MaxDaysToSync.TotalDays + "* interval '1 day') "
                        + "and (di.peer is not null) and di.mediatype in('voice','callback');";
                    break;
                default:
                    throw new NotImplementedException("Database type not implemented for voice analysis");
            }

            // Retrieve conversation IDs.
            DataTable Conversations = DBAdapter.GetSQLTableData(SelectString, "Conversations");
            _logger?.LogInformation("Voice:Data: Found {Count} conversations for voice analysis", Conversations.Rows.Count);

            // Initialize in-memory tables for voice analysis results.
            VoiceOverView = DBAdapter.CreateInMemTable("convvoiceoverviewdata");
            VoiceTopics = DBAdapter.CreateInMemTable("convvoicetopicdetaildata");
            VoiceSentiment = DBAdapter.CreateInMemTable("convvoicesentimentdetaildata");

            // Batching logic: split Conversations into batches of fixed size.
            int MaxRowsToSend = 100;
            int totalPages = (Conversations.Rows.Count % MaxRowsToSend == 0)
                            ? (Conversations.Rows.Count / MaxRowsToSend)
                            : (Conversations.Rows.Count / MaxRowsToSend) + 1;

            // Set the maximum number of concurrent tasks to avoid overwhelming the system
            // Use Environment.ProcessorCount to scale with available CPU cores, but cap at a reasonable maximum
            int maxConcurrentTasks = Environment.ProcessorCount;
            _logger?.LogInformation("Voice:Batch: Processing {Count} conversations in {Pages} batches of {BatchSize} each with max {Concurrent} concurrent tasks",
                Conversations.Rows.Count, totalPages, MaxRowsToSend, maxConcurrentTasks);

            VoiceAnalysis.OverallConversationCount = Conversations.Rows.Count;

            // Create a semaphore to limit concurrent tasks
            using var throttler = new SemaphoreSlim(maxConcurrentTasks);
            var analysisTasks = new List<Task<bool>>();

            // Create a progress counter for logging
            int completedBatches = 0;
            totalBatches = totalPages;

            for (int curPage = 1; curPage <= totalPages; curPage++)
            {
                // Wait for a slot to become available
                await throttler.WaitAsync();

                // Create a local copy of curPage to avoid closure issues
                int batchNumber = curPage;

                DataTable dtTemp = Conversations.AsEnumerable()
                    .Skip((batchNumber - 1) * MaxRowsToSend)
                    .Take(MaxRowsToSend)
                    .CopyToDataTable();
                dtTemp.TableName = "ConversationsTemp";

                // Create a task that releases the semaphore when done and returns success status
                analysisTasks.Add(Task.Run(async () =>
                {
                    try
                    {
                        // Run the voice analysis for this batch
                        bool batchSuccess = await RunVoiceAnalysis(dtTemp, GCData, _enableKnowledgeQuest);

                        // Log progress periodically (every 5 batches or at the end)
                        int completed = Interlocked.Increment(ref completedBatches);
                        if (completed % 5 == 0 || completed == totalPages)
                        {
                            _logger?.LogInformation("Voice:Progress: Completed {Completed}/{Total} batches ({Percent:P0})",
                                completed, totalPages, (double)completed / totalPages);
                        }

                        return batchSuccess;
                    }
                    finally
                    {
                        // Always release the semaphore
                        throttler.Release();
                    }
                }));
            }

            // Wait for all tasks to complete and check results
            bool[] batchResults = await Task.WhenAll(analysisTasks);

            // Count successful and failed batches
            int successfulBatches = batchResults.Count(result => result);
            failedBatches = totalBatches - successfulBatches;
            allBatchesSuccessful = failedBatches == 0;

            _logger?.LogInformation("Voice:Complete: All {Count} voice analysis tasks completed. Success: {Success}/{Total}, Failed: {Failed}",
                analysisTasks.Count, successfulBatches, totalBatches, failedBatches);

            // If any batches failed, log a warning and prevent sync date updates
            if (!allBatchesSuccessful)
            {
                _logger?.LogWarning("Voice:Error: {FailedCount} out of {TotalCount} batches failed processing. " +
                    "Sync dates will not be updated to prevent data gaps.", failedBatches, totalBatches);
            }
            // Upsert results for voice overview.
            if (VoiceOverView.Rows.Count > 0)
            {
                _logger?.LogInformation("VoiceOverview => {Count} rows", VoiceOverView.Rows.Count);

                // Dynamically determine the appropriate date field for diffing
                string? dateField = GetOptimalDateFieldForDiffing("convvoiceoverviewdata");

                if (!string.IsNullOrEmpty(dateField))
                {
                    // Condensed logging: Only log at debug level if enabled
                    if (_logger != null && _logger.IsEnabled(Microsoft.Extensions.Logging.LogLevel.Debug))
                        _logger.LogDebug("Voice:Diff: Using '{DateField}' for convvoiceoverviewdata diffing", dateField);

                    // Create the where condition for the diffing query
                    DateTime startDate = DateTime.UtcNow.AddDays(-30);
                    DateTime endDate = DateTime.UtcNow;

                    string whereCondition = $@"
                        {dateField} >= '{startDate:yyyy-MM-dd HH:mm:ss}'
                        AND {dateField} <= '{endDate:yyyy-MM-dd HH:mm:ss}'
                    ";

                    // Use the centralized diffing implementation
                    DBUtils.DiffingHelper diffHelper = new DBUtils.DiffingHelper(_logger);
                    DataTable diffedVoiceOverView = diffHelper.DiffDataWithCondition(
                        "convvoiceoverviewdata",
                        whereCondition,
                        VoiceOverView,
                        DBAdapter,
                        "keyid"
                    );

                    if (diffedVoiceOverView.Rows.Count > 0)
                    {
                        finalResult = DBAdapter.WriteDynamicSQLData(diffedVoiceOverView, "convvoiceoverviewdata");
                    }
                    else
                    {
                        _logger?.LogInformation("No new or updated rows for VoiceOverview after diffing");
                        finalResult = true;
                    }
                }
                else
                {
                    _logger?.LogInformation("VoiceOverview => No suitable date field found for diffing, treating all data as new");
                    // Write all VoiceOverView data directly without diffing
                    finalResult = DBAdapter.WriteDynamicSQLData(VoiceOverView, "convvoiceoverviewdata");
                }
            }
            else
            {
                _logger?.LogInformation("No rows for VoiceOverview => skipping");
                finalResult = true;
            }

            // Only update sync dates if all batches were successful AND database writes succeeded
            if (finalResult && allBatchesSuccessful)
                finalResult &= GCData.UpdateLastSuccessDate(GCData.DateToSyncFrom.Add(MaxDaysToSync), SyncType);
            else if (!allBatchesSuccessful)
                _logger?.LogWarning("Voice:Error: Batch processing failures detected - not updating VoiceOverview sync date to prevent data gaps");
            else
                _logger?.LogWarning("Voice:Error: VoiceOverview write failed - not updating last sync date");

            // Process VoiceTopics data with diffing
            if (VoiceTopics.Rows.Count > 0)
            {
                _logger?.LogInformation("VoiceTopics => {Count} rows", VoiceTopics.Rows.Count);

                // Dynamically determine the appropriate date field for diffing
                string? dateField = GetOptimalDateFieldForDiffing("convvoicetopicdetaildata");

                if (!string.IsNullOrEmpty(dateField))
                {
                    // Condensed logging: Only log at debug level if enabled
                    if (_logger != null && _logger.IsEnabled(Microsoft.Extensions.Logging.LogLevel.Debug))
                        _logger.LogDebug("Voice:Diff: Using '{DateField}' for convvoicetopicdetaildata diffing", dateField);

                    // Create the where condition for the diffing query
                    DateTime startDate = DateTime.UtcNow.AddDays(-30);
                    DateTime endDate = DateTime.UtcNow;

                    string whereCondition = $@"
                        {dateField} >= '{startDate:yyyy-MM-dd HH:mm:ss}'
                        AND {dateField} <= '{endDate:yyyy-MM-dd HH:mm:ss}'
                    ";

                    // Use the centralized diffing implementation
                    DBUtils.DiffingHelper diffHelper = new DBUtils.DiffingHelper(_logger);
                    DataTable diffedVoiceTopics = diffHelper.DiffDataWithCondition(
                        "convvoicetopicdetaildata",
                        whereCondition,
                        VoiceTopics,
                        DBAdapter,
                        "keyid"
                    );

                    if (diffedVoiceTopics.Rows.Count > 0)
                    {
                        finalResult = DBAdapter.WriteDynamicSQLData(diffedVoiceTopics, "convvoicetopicdetaildata");
                    }
                    else
                    {
                        _logger?.LogInformation("No new or updated rows for VoiceTopics after diffing");
                        finalResult = true;
                    }
                }
                else
                {
                    _logger?.LogInformation("VoiceTopics => No suitable date field found for diffing, treating all data as new");
                    // Write all VoiceTopics data directly without diffing
                    finalResult = DBAdapter.WriteDynamicSQLData(VoiceTopics, "convvoicetopicdetaildata");
                }
            }
            else
            {
                _logger?.LogInformation("Voice:Write: No rows for VoiceTopics - skipping database write");
            }

            // Only update sync dates if all batches were successful AND database writes succeeded
            if (finalResult && allBatchesSuccessful)
                // When in backfill mode, update the backfill table's watermark
                finalResult = GCData.UpdateLastSuccessDate(GCData.DateToSyncFrom.Add(MaxDaysToSync), Backfill ? "convvoicetopicdetaildata_backfill" : "convvoicetopicdetaildata");
            else if (!allBatchesSuccessful)
                _logger?.LogWarning("Voice:Error: Batch processing failures detected - not updating VoiceTopics sync date to prevent data gaps");
            else
                _logger?.LogWarning("Voice:Error: VoiceTopics write failed - not updating last sync date");

            // Process VoiceSentiment data with diffing
            if (VoiceSentiment.Rows.Count > 0)
            {
                _logger?.LogInformation("VoiceSentiment => {Count} rows", VoiceSentiment.Rows.Count);

                // Dynamically determine the appropriate date field for diffing
                string? dateField = GetOptimalDateFieldForDiffing("convvoicesentimentdetaildata");

                if (!string.IsNullOrEmpty(dateField))
                {
                    // Condensed logging: Only log at debug level if enabled
                    if (_logger != null && _logger.IsEnabled(Microsoft.Extensions.Logging.LogLevel.Debug))
                        _logger.LogDebug("Voice:Diff: Using '{DateField}' for convvoicesentimentdetaildata diffing", dateField);

                    // Create the where condition for the diffing query
                    DateTime startDate = DateTime.UtcNow.AddDays(-30);
                    DateTime endDate = DateTime.UtcNow;

                    string whereCondition = $@"
                        {dateField} >= '{startDate:yyyy-MM-dd HH:mm:ss}'
                        AND {dateField} <= '{endDate:yyyy-MM-dd HH:mm:ss}'
                    ";

                    // Use the centralized diffing implementation
                    DBUtils.DiffingHelper diffHelper = new DBUtils.DiffingHelper(_logger);
                    DataTable diffedVoiceSentiment = diffHelper.DiffDataWithCondition(
                        "convvoicesentimentdetaildata",
                        whereCondition,
                        VoiceSentiment,
                        DBAdapter,
                        "keyid"
                    );

                    if (diffedVoiceSentiment.Rows.Count > 0)
                    {
                        finalResult = DBAdapter.WriteDynamicSQLData(diffedVoiceSentiment, "convvoicesentimentdetaildata");
                    }
                    else
                    {
                        _logger?.LogInformation("No new or updated rows for VoiceSentiment after diffing");
                        finalResult = true;
                    }
                }
                else
                {
                    _logger?.LogInformation("VoiceSentiment => No suitable date field found for diffing, treating all data as new");
                    // Write all VoiceSentiment data directly without diffing
                    finalResult = DBAdapter.WriteDynamicSQLData(VoiceSentiment, "convvoicesentimentdetaildata");
                }
            }
            else
            {
                _logger?.LogInformation("Voice:Write: No rows for VoiceSentiment - skipping database write");
            }

            // Only update sync dates if all batches were successful AND database writes succeeded
            if (finalResult && allBatchesSuccessful)
                // When in backfill mode, update the backfill table's watermark
                finalResult = GCData.UpdateLastSuccessDate(GCData.DateToSyncFrom.Add(MaxDaysToSync), Backfill ? "convvoicesentimentdetaildata_backfill" : "convvoicesentimentdetaildata");
            else if (!allBatchesSuccessful)
                _logger?.LogWarning("Voice:Error: Batch processing failures detected - not updating VoiceSentiment sync date to prevent data gaps");
            else
                _logger?.LogWarning("Voice:Error: VoiceSentiment write failed - not updating last sync date");

            // Log final progress summary at the end of the job
            LogProgressSummary(true);

            // Reset all counters for the next job run
            lock (_progressLock)
            {
                _totalConversationsProcessed = 0;
                _totalOverviewRowsAdded = 0;
                _totalTopicRowsAdded = 0;
                _totalSentimentRowsAdded = 0;
                _totalTranscriptsProcessed = 0;
                _totalTranscriptsFailed = 0;
                _totalQueuesVerified = 0;
                _totalQueuesSkipped = 0;
            }

            // Note: Database connection will be closed by Program.cs

            _logger?.LogInformation("Job:Complete: {SyncType} Voice Analysis job finished in {ElapsedSeconds:N2}s",
                SyncType, (DateTime.Now - Start).TotalSeconds);

            // Return success only if all batches were successful AND database operations succeeded
            bool overallSuccess = finalResult && allBatchesSuccessful;

            if (!overallSuccess && allBatchesSuccessful)
                _logger?.LogWarning("Voice:Result: Job completed with database write failures");
            else if (!overallSuccess && !allBatchesSuccessful)
                _logger?.LogWarning("Voice:Result: Job completed with batch processing failures - sync dates not updated");

            return overallSuccess;
        }

        // Create readonly objects for thread-safe locking
        private readonly object _voiceOverViewLock = new object();
        private readonly object _voiceTopicsLock = new object();
        private readonly object _voiceSentimentLock = new object();

        /// <summary>
        /// Checks if the Knowledge Quest license is available
        /// </summary>
        /// <returns>True if the Knowledge Quest license is available, false otherwise</returns>
        private bool HasKnowledgeQuestLicense()
        {
            // For now, return the value of the _enableKnowledgeQuest flag
            // In the future, this will use the LicenseManager to check the license type
            return _enableKnowledgeQuest;
        }

        /// <summary>
        /// Logs a summary of the current progress across all threads
        /// </summary>
        private void LogProgressSummary(bool force = false)
        {
            // Use a lock to ensure thread safety when updating and logging progress
            lock (_progressLock)
            {
                // Only log if we've processed at least PROGRESS_LOG_INTERVAL conversations or if forced
                bool shouldLog = force ||
                    (_totalConversationsProcessed % PROGRESS_LOG_INTERVAL == 0 && _totalConversationsProcessed > 0);

                // Also enforce a minimum time between logs to avoid flooding
                TimeSpan timeSinceLastLog = DateTime.UtcNow - _lastProgressLogTime;
                shouldLog = shouldLog && (force || timeSinceLastLog >= MIN_LOG_INTERVAL);

                if (shouldLog)
                {
                    _logger?.LogInformation(
                        "Voice:Progress: Processed {ConvCount} conversations total | " +
                        "Added {OverviewCount} overview, {TopicCount} topic, {SentimentCount} sentiment rows | " +
                        "Transcripts: {ProcessedCount} processed, {FailedCount} failed | " +
                        "Queues: {VerifiedCount} verified, {SkippedCount} skipped",
                        _totalConversationsProcessed,
                        _totalOverviewRowsAdded,
                        _totalTopicRowsAdded,
                        _totalSentimentRowsAdded,
                        _totalTranscriptsProcessed,
                        _totalTranscriptsFailed,
                        _totalQueuesVerified,
                        _totalQueuesSkipped);

                    _lastProgressLogTime = DateTime.UtcNow;
                }
            }
        }

        /// <summary>
        /// Logs a summary of participant attributes batch processing progress
        /// </summary>
        private void LogParticipantProgressSummary(bool force = false)
        {
            lock (_participantProgressLock)
            {
                // Only log if we've processed significant data or if forced
                bool shouldLog = force ||
                    ((_totalParticipantAttributesWritten + _totalParticipantSummaryWritten + _totalFlowOutcomeWritten) % PARTICIPANT_PROGRESS_LOG_INTERVAL == 0 &&
                     (_totalParticipantAttributesWritten + _totalParticipantSummaryWritten + _totalFlowOutcomeWritten) > 0);

                // Also enforce a minimum time between logs to avoid flooding
                TimeSpan timeSinceLastLog = DateTime.UtcNow - _lastParticipantProgressLogTime;
                shouldLog = shouldLog && (force || timeSinceLastLog >= MIN_LOG_INTERVAL);

                if (shouldLog)
                {
                    int totalProcessed = _totalParticipantAttributesProcessed + _totalParticipantSummaryProcessed + _totalFlowOutcomeProcessed;
                    int totalWritten = _totalParticipantAttributesWritten + _totalParticipantSummaryWritten + _totalFlowOutcomeWritten;
                    int totalErrors = _totalParticipantAttributesErrors + _totalParticipantSummaryErrors + _totalFlowOutcomeErrors;

                    _logger?.LogInformation(
                        "Participant:Progress: Processed {TotalProcessed} rows total, Written {TotalWritten} rows | " +
                        "ParticipantAttributes: {PAProcessed} processed, {PAWritten} written, {PASkipped} skipped, {PAErrors} errors | " +
                        "ParticipantSummary: {PSProcessed} processed, {PSWritten} written, {PSErrors} errors | " +
                        "FlowOutcome: {FOProcessed} processed, {FOWritten} written, {FOErrors} errors",
                        totalProcessed,
                        totalWritten,
                        _totalParticipantAttributesProcessed,
                        _totalParticipantAttributesWritten,
                        _totalParticipantAttributesSkipped,
                        _totalParticipantAttributesErrors,
                        _totalParticipantSummaryProcessed,
                        _totalParticipantSummaryWritten,
                        _totalParticipantSummaryErrors,
                        _totalFlowOutcomeProcessed,
                        _totalFlowOutcomeWritten,
                        _totalFlowOutcomeErrors);

                    _lastParticipantProgressLogTime = DateTime.UtcNow;
                }
            }
        }

        private async Task<bool> RunVoiceAnalysis(DataTable conversationsBatch, GCGetData GCData, bool processTranscripts = false)
        {
            // Set a unique task ID for this batch to correlate logs
            string taskId = $"voice-batch-{Guid.NewGuid().ToString().Substring(0, 8)}";

            try
            {
                // Log at trace level to reduce duplicate logs
                _logger?.LogTrace("Voice:Batch: Processing batch of {Count} conversations with task ID {TaskId}",
                    conversationsBatch.Rows.Count, taskId);

                // Retrieve voice analysis data for this batch asynchronously.
                DataSet interData = await GCData.VoiceAnalysisCombinedDataAsync(conversationsBatch);

                // Process transcripts if requested and Knowledge Quest is enabled
                if (processTranscripts && _enableKnowledgeQuest && HasKnowledgeQuestLicense())
                {
                    // Process transcripts for Knowledge Quest
                    _logger?.LogDebug("Voice:KnowledgeQuest: Processing transcripts for batch with task ID {TaskId}", taskId);
                    await ProcessTranscriptsAsync(conversationsBatch, interData, taskId);
                }

                // Update counters and synchronize access to shared DataTables using readonly lock objects
                int overviewRowsAdded = 0;
                int topicRowsAdded = 0;
                int sentimentRowsAdded = 0;

                lock (_voiceOverViewLock)
                {
                    foreach (DataRow dr in interData.Tables[0].Rows)
                    {
                        // Create a new row and copy values column by column to avoid array length mismatch
                        DataRow newRow = VoiceOverView.NewRow();
                        foreach (DataColumn column in VoiceOverView.Columns)
                        {
                            if (dr.Table.Columns.Contains(column.ColumnName))
                            {
                                newRow[column.ColumnName] = dr[column.ColumnName];
                            }
                        }
                        VoiceOverView.Rows.Add(newRow);
                        overviewRowsAdded++;
                    }
                }
                lock (_voiceTopicsLock)
                {
                    foreach (DataRow dr in interData.Tables[1].Rows)
                    {
                        // Create a new row and copy values column by column to avoid array length mismatch
                        DataRow newRow = VoiceTopics.NewRow();
                        foreach (DataColumn column in VoiceTopics.Columns)
                        {
                            if (dr.Table.Columns.Contains(column.ColumnName))
                            {
                                newRow[column.ColumnName] = dr[column.ColumnName];
                            }
                        }
                        VoiceTopics.Rows.Add(newRow);
                        topicRowsAdded++;
                    }
                }
                lock (_voiceSentimentLock)
                {
                    foreach (DataRow dr in interData.Tables[2].Rows)
                    {
                        // Create a new row and copy values column by column to avoid array length mismatch
                        DataRow newRow = VoiceSentiment.NewRow();
                        foreach (DataColumn column in VoiceSentiment.Columns)
                        {
                            if (dr.Table.Columns.Contains(column.ColumnName))
                            {
                                newRow[column.ColumnName] = dr[column.ColumnName];
                            }
                        }
                        VoiceSentiment.Rows.Add(newRow);
                        sentimentRowsAdded++;
                    }
                }

                // Update global counters atomically
                lock (_progressLock)
                {
                    _totalConversationsProcessed += conversationsBatch.Rows.Count;
                    _totalOverviewRowsAdded += overviewRowsAdded;
                    _totalTopicRowsAdded += topicRowsAdded;
                    _totalSentimentRowsAdded += sentimentRowsAdded;
                }

                // Log progress summary if needed
                LogProgressSummary();

                // Log completion at debug level
                _logger?.LogDebug("Voice:Batch: Processed batch with task ID {TaskId}, added {OverviewCount} overview, {TopicCount} topic, {SentimentCount} sentiment rows",
                    taskId, overviewRowsAdded, topicRowsAdded, sentimentRowsAdded);

                // Return success if we reach this point
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Voice:Error: Failed processing batch of {Count} conversations with task ID {TaskId}",
                    conversationsBatch.Rows.Count, taskId);

                // Return failure when an exception occurs
                return false;
            }
        }

        #endregion

        #region Transcript Processing for Knowledge Quest

        // Constants for transcript processing columns - used to track transcripts sent to Knowledge Quest service
        private const string COLUMN_PROCESSED = "transcript_processed";
        private const string COLUMN_PROCESSED_DATE = "transcript_processed_date";
        private const string COLUMN_PROCESSED_NOTES = "transcript_processed_notes";

        private async Task ProcessTranscriptsAsync(DataTable conversationsBatch, DataSet voiceData, string taskId)
        {
            try
            {
                // Create a VoiceAnalysis instance for verify/ingest operations
                // Create a logger factory to create a typed logger
                ILogger<VoiceAnalysis> typedLogger = _logger != null ?
                    LoggerFactoryExtensions.CreateLogger<VoiceAnalysis>(new LoggerFactory()) : null;
                VoiceAnalysis voiceAnalysis = new VoiceAnalysis(typedLogger);
                voiceAnalysis.Initialize();

                // Get the overview data table (transcript columns are already ensured in VoiceAnalysis)
                DataTable overviewTable = voiceData.Tables[0];

                // First, get all conversation IDs that need processing
                List<string> conversationIds = new List<string>();
                Dictionary<string, DataRow> conversationToOverviewMap = new Dictionary<string, DataRow>();
                Dictionary<string, DataRow> conversationToDataMap = new Dictionary<string, DataRow>();

                foreach (DataRow conversation in conversationsBatch.Rows)
                {
                    string conversationId = conversation["conversationid"].ToString();

                    // Find the corresponding row in the overview table
                    DataRow overviewRow = FindOverviewRow(overviewTable, conversationId);
                    if (overviewRow == null) continue;

                    conversationIds.Add(conversationId);
                    conversationToOverviewMap[conversationId] = overviewRow;
                    conversationToDataMap[conversationId] = conversation;
                }

                // Skip processing if no conversations need processing
                if (conversationIds.Count == 0)
                {
                    _logger?.LogTrace("Voice:Process: No conversations need transcript processing in batch {TaskId}", taskId);
                    return;
                }

                _logger?.LogDebug("Voice:Process: Processing {Count} conversations for Knowledge Quest in batch {TaskId}",
                    conversationIds.Count, taskId);

                // Batch retrieve queue IDs for all conversations
                Dictionary<string, string> conversationToQueueMap = await GetQueueIdsForConversations(conversationIds);

                // Group conversations by queue ID for more efficient processing
                var conversationsByQueue = new Dictionary<string, List<string>>();
                int noQueueIdCount = 0;

                foreach (string conversationId in conversationIds)
                {
                    // Get the queue ID for this conversation
                    if (conversationToQueueMap.TryGetValue(conversationId, out string queueId) && !string.IsNullOrEmpty(queueId))
                    {
                        if (!conversationsByQueue.ContainsKey(queueId))
                        {
                            conversationsByQueue[queueId] = new List<string>();
                        }
                        conversationsByQueue[queueId].Add(conversationId);
                    }
                    else
                    {
                        // Mark as processed with "No queue ID found"
                        MarkAsProcessed(conversationToOverviewMap[conversationId], "No queue ID found");
                        noQueueIdCount++;
                    }
                }

                if (noQueueIdCount > 0)
                {
                    _logger?.LogDebug("Voice:Process: {Count} conversations skipped due to missing queue ID in batch {TaskId}",
                        noQueueIdCount, taskId);
                }

                // Process each queue and its conversations
                int verifiedQueues = 0;
                int skippedQueues = 0;
                int processedTranscripts = 0;
                int failedTranscripts = 0;

                foreach (var queueEntry in conversationsByQueue)
                {
                    string queueId = queueEntry.Key;
                    List<string> queueConversations = queueEntry.Value;

                    // Verify if the queue should be processed
                    bool queueVerified = await voiceAnalysis.VerifyQueueAsync(queueId);

                    if (queueVerified)
                    {
                        verifiedQueues++;

                        // Process all conversations for this verified queue
                        foreach (string conversationId in queueConversations)
                        {
                            DataRow overviewRow = conversationToOverviewMap[conversationId];
                            DataRow conversation = conversationToDataMap[conversationId];

                            // Check if this conversation has already been successfully processed
                            if (IsTranscriptAlreadyProcessed(conversationId))
                            {
                                _logger?.LogTrace("Voice:Process: Conversation {ConvId} already processed, skipping Knowledge Quest ingestion", conversationId);
                                MarkAsProcessed(overviewRow, "Already processed - skipped");
                                processedTranscripts++; // Count as processed since it was already done
                                continue;
                            }

                            // Get the transcript JSON
                            string transcriptJson = await GetTranscriptJson(voiceAnalysis, conversation, conversationId, overviewRow);
                            if (string.IsNullOrEmpty(transcriptJson))
                            {
                                failedTranscripts++;
                                continue;
                            }

                            // Ingest the transcript to Knowledge Quest service for AI processing
                            bool ingestSuccess = await IngestTranscriptWithResult(voiceAnalysis, transcriptJson, conversationId, overviewRow, queueId);
                            if (ingestSuccess)
                            {
                                processedTranscripts++;
                            }
                            else
                            {
                                failedTranscripts++;
                            }
                        }
                    }
                    else
                    {
                        skippedQueues++;

                        // Mark all conversations for this queue as processed but not verified
                        foreach (string conversationId in queueConversations)
                        {
                            MarkAsProcessed(conversationToOverviewMap[conversationId], "Queue not verified for processing");
                        }

                        _logger?.LogDebug("Voice:Verify: Queue {QueueId} not verified for processing, skipped {Count} conversations",
                            queueId, queueConversations.Count);
                    }
                }

                // Update global counters atomically
                lock (_progressLock)
                {
                    _totalTranscriptsProcessed += processedTranscripts;
                    _totalTranscriptsFailed += failedTranscripts;
                    _totalQueuesVerified += verifiedQueues;
                    _totalQueuesSkipped += skippedQueues;
                }

                // Log progress summary
                LogProgressSummary();

                // Log batch summary statistics
                _logger?.LogInformation("Voice:Summary: Batch {TaskId} - Processed {ProcessedCount}/{TotalCount} transcripts, "
                    + "Verified {VerifiedQueues}/{TotalQueues} queues, Failed {FailedCount} transcripts",
                    taskId, processedTranscripts, conversationIds.Count, verifiedQueues,
                    conversationsByQueue.Count, failedTranscripts);

                // Log additional Knowledge Quest ingestion summary
                voiceAnalysis.LogIngestSummary();
            }
            catch (Exception ex)
            {
                _logger?.LogDebug(ex, "Voice:Process: Error processing transcripts for batch with task ID {TaskId}", taskId);
            }
        }



        /// <summary>
        /// Checks if a conversation's transcript has already been successfully processed by Knowledge Quest
        /// by querying the database directly to get the most current status
        /// </summary>
        /// <param name="conversationId">The conversation ID to check</param>
        /// <returns>True if transcript_processed is true in the database, false otherwise</returns>
        private bool IsTranscriptAlreadyProcessed(string conversationId)
        {
            try
            {
                if (string.IsNullOrEmpty(conversationId))
                {
                    return false;
                }

                // Use a database query to check the current status
                DBUtils.DBUtils dbUtils = new DBUtils.DBUtils();
                dbUtils.Initialize();

                // Safely escape the conversation ID to prevent SQL injection
                string safeConversationId = conversationId.Replace("'", "''");

                // Build database-specific query
                string query;
                switch (dbUtils.DBType)
                {
                    case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                        query = $"SELECT transcript_processed FROM convvoiceoverviewdata WHERE conversationid = '{safeConversationId}' LIMIT 1";
                        break;
                    case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                        query = $"SELECT TOP 1 transcript_processed FROM convvoiceoverviewdata WHERE conversationid = '{safeConversationId}'";
                        break;
                    case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                        query = $"SELECT transcript_processed FROM convvoiceoverviewdata WHERE conversationid = '{safeConversationId}' LIMIT 1";
                        break;
                    default:
                        _logger?.LogWarning("Voice:Check: Unsupported database type for transcript status check");
                        return false;
                }

                DataTable result = dbUtils.GetSQLTableData(query, "TranscriptStatus");

                if (result != null && result.Rows.Count > 0)
                {
                    DataRow row = result.Rows[0];
                    if (row["transcript_processed"] != DBNull.Value)
                    {
                        bool isProcessed = Convert.ToBoolean(row["transcript_processed"]);
                        if (isProcessed)
                        {
                            _logger?.LogTrace("Voice:Check: Conversation {ConvId} already processed in database", conversationId);
                            return true;
                        }
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Voice:Check: Error checking transcript processed status for conversation {ConvId}", conversationId);
                // Return false on error to allow processing attempt (fail-safe approach)
                return false;
            }
        }

        private DataRow FindOverviewRow(DataTable overviewTable, string conversationId)
        {
            // Find the corresponding row in the overview table
            DataRow[] matchingRows = overviewTable.Select($"conversationid = '{conversationId}'")
                .Where(r => r[COLUMN_PROCESSED] == DBNull.Value || !(bool)r[COLUMN_PROCESSED])
                .ToArray();

            if (matchingRows.Length == 0)
            {
                _logger?.LogDebug("Voice:Verify: Conversation {ConvId} not found in overview data or already processed", conversationId);
                return null;
            }

            return matchingRows[0];
        }

        private async Task<Dictionary<string, string>> GetQueueIdsForConversations(List<string> conversationIds)
        {
            Dictionary<string, string> result = new Dictionary<string, string>();

            if (conversationIds.Count == 0)
            {
                return result;
            }

            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            try
            {
                // Use a database query to get the queue IDs for all conversations in a single query
                DBUtils.DBUtils dbUtils = new DBUtils.DBUtils();
                dbUtils.Initialize();

                // Deduplicate conversation IDs to optimize query performance
                var distinctConversationIds = conversationIds.Distinct().ToList();

                // Create a comma-separated list of distinct conversation IDs for the IN clause
                string conversationIdList = string.Join("','", distinctConversationIds);

                // Query to get queue IDs for all conversations
                string query = $"SELECT conversationid, queueid FROM detailedinteractiondata WHERE conversationid IN ('{conversationIdList}')";

                _logger?.LogInformation("Voice:Verify: Retrieving queue IDs for {DistinctCount} distinct conversations (deduplicated from {OriginalCount} total)",
                    distinctConversationIds.Count, conversationIds.Count);

                // Only log truncated query in debug mode for troubleshooting (avoid massive log entries)
                if (_logger != null && _logger.IsEnabled(Microsoft.Extensions.Logging.LogLevel.Debug))
                {
                    string truncatedQuery = query.Length > 500 ?
                        query.Substring(0, 500) + "... [TRUNCATED - " + (query.Length - 500) + " more characters]" :
                        query;
                    _logger.LogDebug("Voice:Verify: SQL query (truncated): {Query}", truncatedQuery);
                }
                DataTable queueData = dbUtils.GetSQLTableData(query, "QueueData");

                // Process the results
                foreach (DataRow row in queueData.Rows)
                {
                    string conversationId = row["conversationid"].ToString();

                    if (row["queueid"] != DBNull.Value)
                    {
                        string queueId = row["queueid"].ToString();
                        result[conversationId] = queueId;
                    }
                }

                // Log conversations without queue IDs and runtime information in a single log entry
                int missingQueueIds = 0;
                foreach (string conversationId in conversationIds)
                {
                    if (!result.ContainsKey(conversationId))
                    {
                        missingQueueIds++;
                    }
                }

                stopwatch.Stop();
                _logger?.LogInformation("Voice:Verify: Retrieved {Found}/{Total} queue IDs in {Duration:N3}s | Missing: {Missing} | Batch size: {BatchSize}",
                    result.Count, conversationIds.Count, stopwatch.Elapsed.TotalSeconds, missingQueueIds, conversationIds.Count);

                // Log individual missing queue IDs at warning level if needed
                if (missingQueueIds > 0)
                {
                    foreach (string conversationId in conversationIds)
                    {
                        if (!result.ContainsKey(conversationId))
                        {
                            _logger?.LogWarning("Voice:Verify: No queue ID found for conversation {ConvId}", conversationId);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger?.LogError(ex, "Voice:Verify: Failed to get queue IDs for {Count} conversations after {Duration:N3}s",
                    conversationIds.Count, stopwatch.Elapsed.TotalSeconds);
            }

            return result;
        }

        private async Task<string> GetTranscriptJson(VoiceAnalysis voiceAnalysis, DataRow conversation, string conversationId, DataRow overviewRow)
        {
            try
            {
                // First check if we have a cached transcript
                string cachedTranscript = voiceAnalysis.GetCachedTranscript(conversationId);
                if (!string.IsNullOrEmpty(cachedTranscript))
                {
                    _logger?.LogTrace("Voice:Transcript: Using cached transcript for conversation {ConvId}", conversationId);
                    return cachedTranscript;
                }

                // If not cached, get it from the API
                string peerId = conversation["peer"].ToString();
                _logger?.LogTrace("Voice:Transcript: Getting transcript for conversation {ConvId}", conversationId);

                // First get the transcript URL
                string transcriptUrl = await voiceAnalysis.GetTranscriptUrlAsync(conversationId, peerId);

                if (string.IsNullOrEmpty(transcriptUrl))
                {
                    _logger?.LogTrace("Voice:Transcript: No transcript URL found for conversation {ConvId}", conversationId);
                    MarkAsProcessed(overviewRow, "No transcript URL found");
                    return null;
                }

                // Then download the transcript and cache it with the conversation ID
                string transcriptJson = await voiceAnalysis.DownloadTranscriptAsync(transcriptUrl, conversationId);

                if (string.IsNullOrEmpty(transcriptJson))
                {
                    _logger?.LogTrace("Voice:Transcript: Failed to download transcript for conversation {ConvId}", conversationId);
                    MarkAsProcessed(overviewRow, "Failed to download transcript");
                    return null;
                }

                return transcriptJson;
            }
            catch (Exception ex)
            {
                _logger?.LogTrace(ex, "Voice:Transcript: Error getting transcript for conversation {ConvId}", conversationId);
                MarkAsProcessed(overviewRow, $"Error getting transcript: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Sends the transcript to the Knowledge Quest service for AI processing
        /// Only marks transcript as processed if ALL conditions are met:
        /// 1. Knowledge Quest is enabled via license
        /// 2. Queue was verified for Knowledge Quest processing
        /// 3. Transcript was successfully retrieved
        /// 4. Knowledge Quest API returned success
        /// </summary>
        /// <returns>True if the ingestion was successful, false otherwise</returns>
        private async Task<bool> IngestTranscriptWithResult(VoiceAnalysis voiceAnalysis, string transcriptJson, string conversationId, DataRow overviewRow, string queueId)
        {
            try
            {
                _logger?.LogTrace("Voice:Ingest: Ingesting transcript for conversation {ConvId} in queue {QueueId}", conversationId, queueId);

                // Call the Knowledge Quest API with proper parameters
                bool ingestResult = await voiceAnalysis.IngestTranscriptAsync(transcriptJson, conversationId, queueId);

                if (ingestResult)
                {
                    _logger?.LogTrace("Voice:Ingest: Successfully ingested transcript for conversation {ConvId}", conversationId);

                    // Update the database to mark as successfully processed
                    bool dbUpdateSuccess = voiceAnalysis.UpdateTranscriptProcessedStatus(conversationId, true, "Successfully ingested to Knowledge Quest");
                    if (!dbUpdateSuccess)
                    {
                        _logger?.LogWarning("Voice:Ingest: Failed to update database status for successfully ingested conversation {ConvId}", conversationId);
                    }

                    // Also update the DataRow for immediate use
                    MarkAsProcessed(overviewRow, "Successfully ingested to Knowledge Quest");
                    return true;
                }
                else
                {
                    _logger?.LogTrace("Voice:Ingest: Failed to ingest transcript for conversation {ConvId}", conversationId);

                    // Do NOT mark as processed in database since ingestion failed
                    // Only update the DataRow with failure information for logging
                    MarkAsProcessed(overviewRow, "Knowledge Quest ingest operation failed");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Voice:Ingest: Error ingesting transcript for conversation {ConvId}", conversationId);

                // Do NOT mark as processed in database since an error occurred
                // Only update the DataRow with error information for logging
                MarkAsProcessed(overviewRow, $"Error ingesting transcript to Knowledge Quest: {ex.Message}");
                return false;
            }
        }

        private static void MarkAsProcessed(DataRow row, string notes)
        {
            row[COLUMN_PROCESSED] = true;
            row[COLUMN_PROCESSED_DATE] = DateTime.UtcNow;
            row[COLUMN_PROCESSED_NOTES] = notes;
        }

        #endregion

        #region Dynamic Date Field Detection for Diffing

        /// <summary>
        /// Dynamically determines the optimal date field for diffing operations by inspecting the database schema.
        /// Excludes 'updated' and columns ending with 'ltc', then preferences 'startdate' or 'starttime'.
        /// </summary>
        /// <param name="tableName">The name of the table to inspect</param>
        /// <returns>The optimal date field name, or null if none found</returns>
        private string? GetOptimalDateFieldForDiffing(string tableName)
        {
            try
            {
                // Get the database schema for the specified table
                DBUtils.DBUtils dbUtils = new DBUtils.DBUtils();
                dbUtils.Initialize();

                // Get table schema information using the existing method
                DataTable schemaTable = dbUtils.GetSQLTableSchema(tableName);

                if (schemaTable == null || schemaTable.Columns.Count == 0)
                {
                    _logger?.LogWarning("Voice:Schema: Unable to retrieve schema for table '{TableName}'", tableName);
                    return null;
                }

                // Find all date/datetime columns by examining the DataTable column types
                var dateColumns = new List<string>();

                foreach (DataColumn column in schemaTable.Columns)
                {
                    string columnName = column.ColumnName.ToLowerInvariant();
                    Type columnType = column.DataType;

                    // Check if this is a date/datetime column
                    bool isDateColumn = columnType == typeof(DateTime) ||
                                       columnType == typeof(DateTimeOffset) ||
                                       columnType == typeof(DateTime?) ||
                                       columnType == typeof(DateTimeOffset?);

                    if (isDateColumn && columnName != "updated" && !columnName.EndsWith("ltc"))
                    {
                        dateColumns.Add(columnName);
                    }
                }

                if (dateColumns.Count == 0)
                {
                    _logger?.LogDebug("Voice:Schema: No suitable date fields found for diffing in table '{TableName}'", tableName);
                    return null;
                }

                // Preference order: startdate, starttime, then any other date field
                string[] preferredFields = { "startdate", "starttime" };

                foreach (string preferredField in preferredFields)
                {
                    if (dateColumns.Contains(preferredField))
                    {
                        _logger?.LogDebug("Voice:Schema: Found preferred date field '{DateField}' for table '{TableName}'",
                            preferredField, tableName);
                        return preferredField;
                    }
                }

                // If no preferred fields found, use the first available date column
                string selectedField = dateColumns[0];
                _logger?.LogDebug("Voice:Schema: Using first available date field '{DateField}' for table '{TableName}' (no preferred fields found)",
                    selectedField, tableName);

                return selectedField;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Voice:Schema: Error determining optimal date field for table '{TableName}'", tableName);
                return null;
            }
        }

        /// <summary>
        /// Validates data consistency between conversation summary and participant attributes processing.
        /// Throws an exception if record counts don't match, indicating data integrity issues.
        /// </summary>
        /// <param name="syncType">The type of sync operation being performed</param>
        private void ValidateDataConsistency(string syncType)
        {
            try
            {
                lock (_participantProgressLock)
                {
                    // Only validate if conversation summary data was processed
                    if (_totalConvSummaryProcessed > 0)
                    {
                        _logger?.LogInformation("DataConsistency:Validation: Starting data consistency validation for {SyncType}", syncType);

                        // Get unique conversation count from participant summary data (this represents conversations with participants)
                        // Participant summary data has one record per participant, so we need to count unique conversations
                        int conversationsWithParticipants = GetUniqueConversationCountFromParticipantSummary();

                        _logger?.LogInformation("DataConsistency:Counts: ConvSummary processed: {ConvSummaryCount}, ParticipantSummary processed: {ParticipantSummaryCount}, Unique conversations with participants: {ConversationsWithParticipants}, ParticipantAttributes processed: {ParticipantAttributesCount}",
                            _totalConvSummaryProcessed, _totalParticipantSummaryProcessed, conversationsWithParticipants, _totalParticipantAttributesProcessed);

                        // Calculate true conversations without participants
                        int conversationsWithoutParticipants = _totalConvSummaryProcessed - conversationsWithParticipants;

                        // Validate that conversations with participants doesn't exceed total conversations
                        if (conversationsWithParticipants > _totalConvSummaryProcessed)
                        {
                            // This would be a real problem - more conversations with participants than total conversations
                            string errorMessage = $"CRITICAL DATA INCONSISTENCY DETECTED: More conversations with participants ({conversationsWithParticipants}) than total conversation summaries ({_totalConvSummaryProcessed}). " +
                                $"This indicates data processing inconsistency in {syncType} job operation. " +
                                $"Data synchronization integrity has been compromised and requires immediate investigation.";

                            _logger?.LogError("DataConsistency:CRITICAL: {ErrorMessage}", errorMessage);

                            // Throw exception to prevent job from appearing successful when data integrity issues exist
                            throw new InvalidOperationException(errorMessage);
                        }
                        else
                        {
                            double participantPercentage = _totalConvSummaryProcessed > 0 ?
                                (conversationsWithParticipants * 100.0) / _totalConvSummaryProcessed : 0;
                            double attributePercentage = conversationsWithParticipants > 0 ?
                                (_totalParticipantAttributesProcessed * 100.0) / conversationsWithParticipants : 0;

                            _logger?.LogInformation("DataConsistency:SUCCESS: {ConvSummary} total conversations, {ConversationsWithParticipants} with participants ({ParticipantPercentage:F1}%), {ConversationsWithoutParticipants} without participants, {ParticipantAttributes} with attributes ({AttributePercentage:F1}% of conversations with participants)",
                                _totalConvSummaryProcessed, conversationsWithParticipants, participantPercentage, conversationsWithoutParticipants, _totalParticipantAttributesProcessed, attributePercentage);
                        }
                    }
                    else
                    {
                        _logger?.LogInformation("DataConsistency:Skip: No data processed for validation (ConvSummary: {ConvSummaryCount}, ParticipantAttributes: {ParticipantAttributesCount})",
                            _totalConvSummaryProcessed, _totalParticipantAttributesProcessed);
                    }
                }
            }
            catch (Exception ex) when (!(ex is InvalidOperationException))
            {
                _logger?.LogError(ex, "DataConsistency:Error: Unexpected error during data consistency validation for {SyncType}", syncType);
                throw new InvalidOperationException($"CRITICAL: Data consistency validation failed due to unexpected error in {syncType} job. Process terminated to prevent potential data corruption.", ex);
            }
        }

        /// <summary>
        /// Gets the count of unique conversations that have participants by analyzing the participant summary data.
        /// Since participant summary has one record per participant, we need to count unique conversation IDs.
        /// </summary>
        /// <returns>Number of unique conversations that have participants</returns>
        private int GetUniqueConversationCountFromParticipantSummary()
        {
            try
            {
                // For now, use a simple approximation based on the participant summary count
                // This assumes an average number of participants per conversation
                // In most contact centers, conversations typically have 1-3 participants (customer + agent(s))

                if (_totalParticipantSummaryProcessed == 0)
                {
                    return 0;
                }

                // Use a conservative estimate: assume average of 2 participants per conversation
                // This provides a reasonable approximation without requiring complex data analysis
                int estimatedConversationsWithParticipants = _totalParticipantSummaryProcessed / 2;

                // Ensure we don't exceed the total conversation count
                return Math.Min(estimatedConversationsWithParticipants, _totalConvSummaryProcessed);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Error calculating unique conversation count from participant summary, using participant summary count as fallback");
                // Fallback: use the participant summary count as a conservative upper bound
                return Math.Min(_totalParticipantSummaryProcessed, _totalConvSummaryProcessed);
            }
        }

        #endregion
    }
}
